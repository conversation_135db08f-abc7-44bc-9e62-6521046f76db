#!/usr/bin/env python
"""
Setup basic data for Django ERP system
Run this script to create minimal test data
"""
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import Company, Partner, Currency, Country
from accounting.models import AccountGroup, AccountAccount, AccountJournal

def setup_basic_data():
    print("🚀 Setting up basic data for Django ERP...")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin')
        admin_user.save()
        print(f"✅ Created admin user (password: admin)")
    else:
        print(f"✅ Admin user exists")

    # Create Currency
    usd, created = Currency.objects.get_or_create(
        name='USD',
        defaults={
            'symbol': '$',
            'decimal_places': 2,
            'full_name': 'US Dollar',
            'position': 'before',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"✅ Currency USD: {'created' if created else 'exists'}")

    # Create Country
    usa, created = Country.objects.get_or_create(
        code='US',
        defaults={
            'name': 'United States',
            'phone_code': 1,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"✅ Country US: {'created' if created else 'exists'}")

    # Create Company
    try:
        company, created = Company.objects.get_or_create(
            code='DEMO',
            defaults={
                'name': 'Demo Company',
                'currency': usd,
                'email': '<EMAIL>',
                'phone': '******-0123',
                'street': '123 Business St',
                'city': 'New York',
                'zip': '10001',
                'country': usa,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
    except Exception as e:
        # If there's a conflict, try to get existing company
        company = Company.objects.filter(code='DEMO').first()
        if not company:
            company = Company.objects.first()  # Get any existing company
        created = False
    print(f"✅ Company: {'created' if created else 'exists'}")

    # Create Account Groups
    asset_group, created = AccountGroup.objects.get_or_create(
        name='Assets',
        company=company,
        defaults={
            'code_prefix_start': '1000',
            'code_prefix_end': '1999',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"✅ Asset Group: {'created' if created else 'exists'}")

    # Create basic accounts
    accounts_data = [
        ('1000', 'Cash', 'asset_current'),
        ('1100', 'Bank', 'asset_current'),
        ('4000', 'Sales Revenue', 'income'),
        ('5000', 'Expenses', 'expense'),
        ('2000', 'Accounts Payable', 'liability_current'),
    ]

    for code, name, account_type in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'group': asset_group if account_type.startswith('asset') else None,
                'reconcile': False,
                'currency': usd,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"✅ Account {code}: {'created' if created else 'exists'}")

    # Create Journals
    cash_account = AccountAccount.objects.get(code='1000', company=company)
    bank_account = AccountAccount.objects.get(code='1100', company=company)
    
    journals_data = [
        ('GJ', 'General Journal', 'general', None),
        ('BNK', 'Bank Journal', 'bank', bank_account),
        ('CSH', 'Cash Journal', 'cash', cash_account),
        ('SAL', 'Sales Journal', 'sale', None),
        ('PUR', 'Purchase Journal', 'purchase', None),
    ]

    for code, name, journal_type, default_account in journals_data:
        journal, created = AccountJournal.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'type': journal_type,
                'default_account': default_account,
                'currency': usd,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"✅ Journal {code}: {'created' if created else 'exists'}")

    # Create basic partners
    partners_data = [
        ('ABC Corp', True, 1, 0, '<EMAIL>'),
        ('XYZ Ltd', True, 1, 0, '<EMAIL>'),
        ('Office Supplies Co', True, 0, 1, '<EMAIL>'),
    ]

    for name, is_company, customer_rank, supplier_rank, email in partners_data:
        partner, created = Partner.objects.get_or_create(
            name=name,
            defaults={
                'is_company': is_company,
                'customer_rank': customer_rank,
                'supplier_rank': supplier_rank,
                'email': email,
                'country': usa,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"✅ Partner {name}: {'created' if created else 'exists'}")

    print("\n🎉 Basic data setup completed!")
    print("📊 Summary:")
    print(f"   - Companies: {Company.objects.count()}")
    print(f"   - Currencies: {Currency.objects.count()}")
    print(f"   - Accounts: {AccountAccount.objects.count()}")
    print(f"   - Journals: {AccountJournal.objects.count()}")
    print(f"   - Partners: {Partner.objects.count()}")
    print("\n🌐 You can now:")
    print("   - Access admin at: http://localhost:8000/admin/")
    print("   - Login with: admin / admin")
    print("   - Create journal entries at: http://localhost:3000/accounting/journal-entries/create")

if __name__ == "__main__":
    setup_basic_data()
