from django.core.management.base import BaseCommand
from django.db import transaction
from accounting.models import AccountMove, AccountMoveLine
from decimal import Decimal


class Command(BaseCommand):
    help = 'Fix unbalanced journal entries in the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )
        parser.add_argument(
            '--delete-unbalanced',
            action='store_true',
            help='Delete unbalanced entries instead of trying to fix them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        delete_unbalanced = options['delete_unbalanced']
        
        self.stdout.write("Checking for unbalanced journal entries...")
        
        unbalanced_moves = []
        
        # Check all moves for balance issues
        for move in AccountMove.objects.all():
            try:
                total_debit = sum(line.debit for line in move.line_ids.all())
                total_credit = sum(line.credit for line in move.line_ids.all())
                difference = abs(total_debit - total_credit)
                
                if difference > Decimal('0.01'):  # Allow small rounding differences
                    unbalanced_moves.append({
                        'move': move,
                        'total_debit': total_debit,
                        'total_credit': total_credit,
                        'difference': difference
                    })
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error checking move {move.id}: {e}')
                )
        
        if not unbalanced_moves:
            self.stdout.write(
                self.style.SUCCESS('No unbalanced journal entries found!')
            )
            return
        
        self.stdout.write(
            self.style.WARNING(f'Found {len(unbalanced_moves)} unbalanced entries:')
        )
        
        for item in unbalanced_moves:
            move = item['move']
            self.stdout.write(
                f"Move ID {move.id}: Debits={item['total_debit']}, "
                f"Credits={item['total_credit']}, Difference={item['difference']}"
            )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Dry run mode - no changes made')
            )
            return
        
        # Fix or delete unbalanced entries
        with transaction.atomic():
            for item in unbalanced_moves:
                move = item['move']
                
                if delete_unbalanced:
                    # Delete the unbalanced entry
                    self.stdout.write(f'Deleting unbalanced move {move.id}...')
                    move.delete()
                else:
                    # Try to fix by removing all lines (reset to empty draft)
                    self.stdout.write(f'Fixing unbalanced move {move.id}...')
                    move.line_ids.all().delete()
                    move.state = 'draft'
                    move.save()
        
        action = 'deleted' if delete_unbalanced else 'fixed'
        self.stdout.write(
            self.style.SUCCESS(f'Successfully {action} {len(unbalanced_moves)} unbalanced entries!')
        )
