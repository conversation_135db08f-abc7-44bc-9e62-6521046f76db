import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Button, Input } from '../../components/ui';
import './LoginPage.css';

const LoginPage = () => {
  const navigate = useNavigate();
  const { login, isLoading, error, clearError, isAuthenticated, user } = useAuth();
  const [formData, setFormData] = useState({
    username: '', // Changed from email to username to match Django
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [rememberMe, setRememberMe] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    console.log('Auth state changed:', { isAuthenticated, user, isLoading });
    if (isAuthenticated && !isLoading) {
      console.log('User is authenticated, redirecting to dashboard...');
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  // Clear auth errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]); // Include clearError in dependencies

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear auth error
    if (error) {
      clearError();
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('Form submitted with data:', formData);
    console.log('Current auth state:', { isAuthenticated, isLoading, error, user });

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    try {
      console.log('Calling login function...');
      const result = await login(formData);
      console.log('Login result:', result);

      if (result.success) {
        console.log('Login successful:', result.user);
        // Redirect will be handled by useEffect when isAuthenticated changes
      } else {
        // Error is handled by AuthContext and displayed via error state
        console.error('Login failed:', result.error);
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  return (
    <div className="login-page">
      <div className="login-container">
        <div className="login-card">
          {/* Header */}
          <div className="login-header">
            <div className="login-logo">
              <i className="fas fa-cube"></i>
              <span>ERP System</span>
            </div>
            <h1 className="login-title">Sign in to your account</h1>
            <p className="login-subtitle">
              Welcome back! Please enter your details.
            </p>
          </div>

          {/* Form */}
          <form className="login-form" onSubmit={handleSubmit}>
            {error && (
              <div className="login-error-banner">
                <i className="fas fa-exclamation-triangle"></i>
                <span>{error}</span>
              </div>
            )}

            <Input
              type="text"
              name="username"
              label="Username"
              placeholder="Enter your username"
              value={formData.username}
              onChange={handleInputChange}
              error={errors.username}
              icon="fas fa-user"
              required
              autoComplete="username"
            />

            <Input
              type="password"
              name="password"
              label="Password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange}
              error={errors.password}
              required
              autoComplete="current-password"
            />

            <div className="login-options">
              <label className="login-checkbox">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <span className="checkmark"></span>
                <span className="checkbox-text">Remember me</span>
              </label>
              
              <a href="#forgot" className="login-forgot-link">
                Forgot password?
              </a>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="large"
              fullWidth
              loading={isLoading}
              disabled={isLoading}
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          {/* Footer */}
          <div className="login-footer">
            <p>
              Don't have an account?{' '}
              <a href="#signup" className="login-signup-link">
                Contact your administrator
              </a>
            </p>
          </div>
        </div>

        {/* Background Pattern */}
        <div className="login-background">
          <div className="login-pattern"></div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
