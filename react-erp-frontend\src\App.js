import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { StyleSheetManager } from 'styled-components';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginPage from './pages/auth/LoginPage';
import Dashboard from './pages/dashboard/Dashboard';
import AccountingDashboard from './pages/accounting/AccountingDashboard';
import ChartOfAccounts from './pages/accounting/ChartOfAccounts';
import AccountTypeManagement from './pages/accounting/AccountTypeManagement';
import JournalEntryList from './pages/accounting/JournalEntryList';
import JournalEntryForm from './pages/accounting/JournalEntryForm';
import JournalTypes from './pages/accounting/JournalTypes';
import JournalList from './pages/accounting/JournalList';
import JournalForm from './pages/accounting/JournalForm';
import ApiTest from './pages/test/ApiTest';
import DropdownTest from './pages/test/DropdownTest';
import AuthTest from './pages/test/AuthTest';
import ComponentTest from './pages/test/ComponentTest';
import SimpleJournalEntryTest from './pages/test/SimpleJournalEntryTest';
import CompanySettings from './pages/settings/CompanySettings';
import TaxSettings from './pages/settings/TaxSettings';
import TaxForm from './pages/settings/TaxForm';
import CompanySetupWizard from './pages/settings/CompanySetupWizard';
import CompanyConnectionTest from './pages/settings/CompanyConnectionTest';
import ComponentShowcase from './pages/ComponentShowcase';

// Sales Module
import SalesDashboard from './pages/sales/SalesDashboard';
import CustomerList from './pages/sales/CustomerList';
import CustomerForm from './pages/sales/CustomerForm';

// Purchase Module
import PurchasesDashboard from './pages/purchases/PurchasesDashboard';
import VendorList from './pages/purchases/VendorList';
import VendorForm from './pages/purchases/VendorForm';

// HR Module
import HRDashboard from './pages/hr/HRDashboard';
import EmployeeList from './pages/hr/EmployeeList';
import EmployeeForm from './pages/hr/EmployeeForm';
import './styles/globals.css';

// Global prop filter for styled-components
const shouldForwardProp = (prop, defaultValidatorFn) => {
  // Filter out common styling props that shouldn't be forwarded to DOM
  const styledProps = [
    'hasError', 'hasLeftIcon', 'hasRightIcon', 'variant', 'size',
    'currencyPosition', 'width', 'columns', 'inline', 'required',
    'loading', 'active', 'checked', 'selected'
  ];

  if (styledProps.includes(prop)) {
    return false;
  }

  return typeof defaultValidatorFn === 'function' ? defaultValidatorFn(prop) : true;
};

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: 'var(--odoo-bg-secondary)'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '1rem'
        }}>
          <i className="fas fa-spinner fa-spin" style={{
            fontSize: '2rem',
            color: 'var(--odoo-primary)'
          }}></i>
          <p style={{
            color: 'var(--odoo-text-secondary)',
            margin: 0
          }}>Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Main App Content Component
const AppContent = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<LoginPage />} />

      {/* Protected Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <Dashboard />
        </ProtectedRoute>
      } />

      <Route path="/accounting" element={
        <ProtectedRoute>
          <AccountingDashboard />
        </ProtectedRoute>
      } />

      <Route path="/accounting/chart-of-accounts" element={
        <ProtectedRoute>
          <ChartOfAccounts />
        </ProtectedRoute>
      } />

      <Route path="/accounting/account-types" element={
        <ProtectedRoute>
          <AccountTypeManagement />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journal-entries" element={
        <ProtectedRoute>
          <JournalEntryList />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journal-entries/create" element={
        <ProtectedRoute>
          <JournalEntryForm />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journal-entries/edit/:id" element={
        <ProtectedRoute>
          <JournalEntryForm />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journal-entries/view/:id" element={
        <ProtectedRoute>
          <JournalEntryForm />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journal-types" element={
        <ProtectedRoute>
          <JournalTypes />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journals" element={
        <ProtectedRoute>
          <JournalList />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journals/create" element={
        <ProtectedRoute>
          <JournalForm />
        </ProtectedRoute>
      } />

      <Route path="/accounting/journals/:id/edit" element={
        <ProtectedRoute>
          <JournalForm />
        </ProtectedRoute>
      } />

      <Route path="/test/api" element={
        <ProtectedRoute>
          <ApiTest />
        </ProtectedRoute>
      } />

      <Route path="/test/dropdown" element={
        <ProtectedRoute>
          <DropdownTest />
        </ProtectedRoute>
      } />

      <Route path="/test/auth" element={
        <ProtectedRoute>
          <AuthTest />
        </ProtectedRoute>
      } />

      <Route path="/test/components" element={
        <ProtectedRoute>
          <ComponentTest />
        </ProtectedRoute>
      } />

      <Route path="/test/simple-journal" element={
        <ProtectedRoute>
          <SimpleJournalEntryTest />
        </ProtectedRoute>
      } />

      <Route path="/settings/company" element={
        <ProtectedRoute>
          <CompanySettings />
        </ProtectedRoute>
      } />

      <Route path="/settings/taxes" element={
        <ProtectedRoute>
          <TaxSettings />
        </ProtectedRoute>
      } />

      <Route path="/settings/taxes/create" element={
        <ProtectedRoute>
          <TaxForm />
        </ProtectedRoute>
      } />

      <Route path="/settings/taxes/edit/:id" element={
        <ProtectedRoute>
          <TaxForm />
        </ProtectedRoute>
      } />

      <Route path="/settings/company/setup" element={
        <ProtectedRoute>
          <CompanySetupWizard />
        </ProtectedRoute>
      } />

      <Route path="/settings/test" element={
        <ProtectedRoute>
          <CompanyConnectionTest />
        </ProtectedRoute>
      } />

      <Route path="/components" element={
        <ProtectedRoute>
          <ComponentShowcase />
        </ProtectedRoute>
      } />

      {/* Sales Module Routes */}
      <Route path="/sales" element={
        <ProtectedRoute>
          <SalesDashboard />
        </ProtectedRoute>
      } />
      <Route path="/sales/customers" element={
        <ProtectedRoute>
          <CustomerList />
        </ProtectedRoute>
      } />
      <Route path="/sales/customers/create" element={
        <ProtectedRoute>
          <CustomerForm />
        </ProtectedRoute>
      } />
      <Route path="/sales/customers/edit/:id" element={
        <ProtectedRoute>
          <CustomerForm />
        </ProtectedRoute>
      } />

      {/* Purchase Module Routes */}
      <Route path="/purchases" element={
        <ProtectedRoute>
          <PurchasesDashboard />
        </ProtectedRoute>
      } />
      <Route path="/purchases/vendors" element={
        <ProtectedRoute>
          <VendorList />
        </ProtectedRoute>
      } />
      <Route path="/purchases/vendors/create" element={
        <ProtectedRoute>
          <VendorForm />
        </ProtectedRoute>
      } />
      <Route path="/purchases/vendors/edit/:id" element={
        <ProtectedRoute>
          <VendorForm />
        </ProtectedRoute>
      } />

      {/* HR Module Routes */}
      <Route path="/hr" element={
        <ProtectedRoute>
          <HRDashboard />
        </ProtectedRoute>
      } />
      <Route path="/hr/employees" element={
        <ProtectedRoute>
          <EmployeeList />
        </ProtectedRoute>
      } />
      <Route path="/hr/employees/create" element={
        <ProtectedRoute>
          <EmployeeForm />
        </ProtectedRoute>
      } />
      <Route path="/hr/employees/edit/:id" element={
        <ProtectedRoute>
          <EmployeeForm />
        </ProtectedRoute>
      } />

      {/* Catch all route - redirect to dashboard */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <StyleSheetManager shouldForwardProp={shouldForwardProp}>
      <Router>
        <AuthProvider>
          <div className="App">
            <AppContent />
          </div>
        </AuthProvider>
      </Router>
    </StyleSheetManager>
  );
}

export default App;
