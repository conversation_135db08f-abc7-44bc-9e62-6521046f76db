import React, { useState, useEffect } from 'react';
import { accountingAPI, coreAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const ApiTest = () => {
  const { user, isAuthenticated } = useAuth();
  const [results, setResults] = useState({
    accounts: { loading: true, data: null, error: null },
    journals: { loading: true, data: null, error: null },
    partners: { loading: true, data: null, error: null },
    taxes: { loading: true, data: null, error: null }
  });

  useEffect(() => {
    const testAPIs = async () => {
      // Test Accounts API
      try {
        console.log('Testing accounts API...');
        const accountsRes = await accountingAPI.getAccounts();
        console.log('Accounts response:', accountsRes);
        setResults(prev => ({
          ...prev,
          accounts: { loading: false, data: accountsRes, error: null }
        }));
      } catch (error) {
        console.error('Accounts API error:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          statusText: error.response?.statusText
        });
        setResults(prev => ({
          ...prev,
          accounts: { loading: false, data: null, error: `${error.message} (Status: ${error.response?.status})` }
        }));
      }

      // Test Journals API
      try {
        console.log('Testing journals API...');
        const journalsRes = await accountingAPI.getJournals();
        console.log('Journals response:', journalsRes);
        setResults(prev => ({
          ...prev,
          journals: { loading: false, data: journalsRes, error: null }
        }));
      } catch (error) {
        console.error('Journals API error:', error);
        setResults(prev => ({
          ...prev,
          journals: { loading: false, data: null, error: `${error.message} (Status: ${error.response?.status})` }
        }));
      }

      // Test Partners API
      try {
        console.log('Testing partners API...');
        const partnersRes = await coreAPI.getPartners();
        console.log('Partners response:', partnersRes);
        setResults(prev => ({
          ...prev,
          partners: { loading: false, data: partnersRes, error: null }
        }));
      } catch (error) {
        console.error('Partners API error:', error);
        setResults(prev => ({
          ...prev,
          partners: { loading: false, data: null, error: error.message }
        }));
      }

      // Test Taxes API
      try {
        console.log('Testing taxes API...');
        const taxesRes = await accountingAPI.getTaxes();
        console.log('Taxes response:', taxesRes);
        setResults(prev => ({
          ...prev,
          taxes: { loading: false, data: taxesRes, error: null }
        }));
      } catch (error) {
        console.error('Taxes API error:', error);
        setResults(prev => ({
          ...prev,
          taxes: { loading: false, data: null, error: error.message }
        }));
      }
    };

    testAPIs();
  }, []);

  const renderApiResult = (name, result) => (
    <div style={{ 
      margin: '20px 0', 
      padding: '15px', 
      border: '1px solid #ddd', 
      borderRadius: '5px',
      backgroundColor: result.error ? '#ffe6e6' : result.loading ? '#fff3cd' : '#e6ffe6'
    }}>
      <h3>{name} API</h3>
      {result.loading && <p>Loading...</p>}
      {result.error && <p style={{ color: 'red' }}>Error: {result.error}</p>}
      {result.data && (
        <div>
          <p><strong>Status:</strong> Success</p>
          <p><strong>Data Type:</strong> {Array.isArray(result.data) ? 'Array' : typeof result.data}</p>
          {Array.isArray(result.data) && <p><strong>Count:</strong> {result.data.length}</p>}
          {result.data.results && <p><strong>Results Count:</strong> {result.data.results.length}</p>}
          <details>
            <summary>Raw Data (click to expand)</summary>
            <pre style={{ 
              background: '#f5f5f5', 
              padding: '10px', 
              overflow: 'auto', 
              maxHeight: '200px',
              fontSize: '12px'
            }}>
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>API Test Page</h1>
      <p>This page tests all the API endpoints used in journal entry creation.</p>

      <div style={{
        margin: '20px 0',
        padding: '15px',
        border: '1px solid #ddd',
        borderRadius: '5px',
        backgroundColor: '#f8f9fa'
      }}>
        <h3>Authentication Status</h3>
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        <p><strong>User:</strong> {user ? user.username : 'Not logged in'}</p>
        <p><strong>Access Token:</strong> {localStorage.getItem('access_token') ? 'Present' : 'Missing'}</p>
        <p><strong>Refresh Token:</strong> {localStorage.getItem('refresh_token') ? 'Present' : 'Missing'}</p>
      </div>

      {renderApiResult('Accounts', results.accounts)}
      {renderApiResult('Journals', results.journals)}
      {renderApiResult('Partners', results.partners)}
      {renderApiResult('Taxes', results.taxes)}
    </div>
  );
};

export default ApiTest;
