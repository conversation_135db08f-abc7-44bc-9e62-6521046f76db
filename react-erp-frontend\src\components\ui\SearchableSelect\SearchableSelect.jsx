import React, { useState, useEffect, useRef, forwardRef } from 'react';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';
import { theme } from '../theme';
import { InputContainer, InputLabel, ErrorMessage, HelpText } from '../Input/Input.styles';

const SelectContainer = styled.div`
  position: relative;
  width: 100%;
  z-index: 1000; /* Ensure dropdown appears above other elements */
`;

const SelectInput = styled.input.withConfig({
  shouldForwardProp: (prop) => !['minWidth', 'width', 'fontSize', 'hasError'].includes(prop),
})`
  width: 100%;
  padding: 4px 8px;
  font-size: ${props => props.fontSize || '12px'};
  font-family: ${theme.typography.fontFamily.odoo};
  font-weight: 400;
  color: #212529;
  background-color: transparent;
  border: none;
  border-bottom: 1px solid transparent;
  border-radius: 0;
  transition: all 0.15s ease-in-out;
  line-height: 1.42857;
  cursor: pointer;

  &::placeholder {
    color: #6c757d;
    font-family: ${theme.typography.fontFamily.odoo};
    font-weight: 400;
  }

  &:hover:not(:disabled) {
    border-bottom-color: #875a7b;
  }

  &:focus {
    outline: none;
    border-bottom-color: #875a7b;
    box-shadow: none;
  }

  &:disabled {
    background-color: transparent;
    color: ${theme.colors.textMuted};
    cursor: not-allowed;
    opacity: 0.6;
  }

  ${props => props.hasError && css`
    border-bottom-color: ${theme.colors.danger};

    &:focus {
      border-bottom-color: ${theme.colors.danger};
    }
  `}
`;

const DropdownIcon = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isOpen'].includes(prop),
})`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: #6c757d;
  font-size: 12px;

  ${props => props.isOpen && css`
    transform: translateY(-50%) rotate(180deg);
  `}
`;

const DropdownList = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isOpen'].includes(prop),
})`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 3px 3px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 9999;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  ${props => !props.isOpen && css`
    display: none;
  `}
`;

const DropdownItem = styled.div`
  padding: 10px 12px;
  font-size: 13px;
  font-family: ${theme.typography.fontFamily.odoo};
  color: #212529 !important;
  background-color: white !important;
  cursor: pointer;
  border-bottom: 1px solid #f8f9fa;
  user-select: none;
  transition: background-color 0.1s ease;

  &:hover {
    background-color: #f8f9fa !important;
    color: #212529 !important;
  }

  &:active {
    background-color: #e9ecef !important;
    color: #212529 !important;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const NoResults = styled.div`
  padding: 8px 12px;
  font-size: 13px;
  font-family: ${theme.typography.fontFamily.odoo};
  color: #6c757d;
  font-style: italic;
`;

const SearchableSelect = forwardRef(({
  label,
  placeholder = 'Select an option...',
  value,
  onChange,
  onBlur,
  onFocus,
  options = [],
  error,
  helpText,
  disabled = false,
  required = false,
  className = '',
  id,
  name,
  searchable = true,
  fontSize = '12px',
  ...props
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [displayValue, setDisplayValue] = useState('');
  const containerRef = useRef(null);
  const inputRef = useRef(null);

  const inputId = id || name || `searchable-select-${Math.random().toString(36).substr(2, 9)}`;

  // Update display value when value prop changes
  useEffect(() => {
    if (Array.isArray(options) && options.length > 0) {
      const selectedOption = options.find(option => option.value === value);
      setDisplayValue(selectedOption ? selectedOption.label : '');
      setSearchTerm('');
    } else {
      setDisplayValue('');
      setSearchTerm('');
    }
  }, [value, options]);

  // Filter options based on search term
  const filteredOptions = Array.isArray(options)
    ? (searchable && searchTerm
        ? options.filter(option =>
            option && option.label && option.label.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : options
      )
    : [];

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputClick = () => {
    console.log('SearchableSelect: Input clicked', {
      disabled,
      isOpen,
      optionsCount: options.length,
      options: options.slice(0, 3), // Show first 3 options for debugging
      value,
      displayValue
    });
    if (!disabled) {
      setIsOpen(!isOpen);
      if (searchable) {
        setSearchTerm(displayValue);
        setDisplayValue('');
      }
    }
  };

  const handleInputChange = (e) => {
    if (searchable) {
      setSearchTerm(e.target.value);
      setDisplayValue(e.target.value);
    }
  };

  const handleOptionClick = (option, event) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    console.log('Option clicked:', option);

    setDisplayValue(option.label);
    setSearchTerm('');
    setIsOpen(false);

    // Use setTimeout to ensure state updates complete before onChange
    setTimeout(() => {
      if (onChange) {
        onChange({
          target: {
            name: name,
            value: option.value,
          },
        });
      }
    }, 0);
  };

  const handleInputFocus = (e) => {
    if (onFocus) onFocus(e);
  };

  const handleInputBlur = (e) => {
    // Delay blur to allow option click
    setTimeout(() => {
      if (!containerRef.current?.contains(document.activeElement)) {
        setIsOpen(false);
        setSearchTerm('');
        if (onBlur) onBlur(e);
      }
    }, 150);
  };

  return (
    <InputContainer className={className}>
      {label && (
        <InputLabel htmlFor={inputId} required={required}>
          {label}
        </InputLabel>
      )}
      
      <SelectContainer ref={containerRef}>
        <SelectInput
          ref={inputRef}
          id={inputId}
          name={name}
          type="text"
          placeholder={placeholder}
          value={isOpen && searchable ? searchTerm : displayValue}
          onChange={handleInputChange}
          onClick={handleInputClick}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          disabled={disabled}
          required={required}
          hasError={!!error}
          readOnly={!searchable}
          autoComplete="off"
          fontSize={fontSize}
          {...props}
        />
        
        <DropdownIcon isOpen={isOpen}>
          <i className="fas fa-chevron-down" />
        </DropdownIcon>
        
        <DropdownList isOpen={isOpen}>
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <DropdownItem
                key={option.value}
                onMouseDown={(e) => handleOptionClick(option, e)}
                onClick={(e) => e.preventDefault()}
                style={{
                  backgroundColor: 'white !important',
                  color: '#212529 !important'
                }}
              >
                {option.label}
              </DropdownItem>
            ))
          ) : (
            <NoResults>
              {options.length === 0 ? 'No options available' : 'No results found'}
            </NoResults>
          )}
        </DropdownList>
      </SelectContainer>
      
      {error && (
        <ErrorMessage>
          <i className="fas fa-exclamation-circle" />
          <span>{error}</span>
        </ErrorMessage>
      )}
      
      {helpText && !error && (
        <HelpText>{helpText}</HelpText>
      )}
    </InputContainer>
  );
});

SearchableSelect.displayName = 'SearchableSelect';

SearchableSelect.propTypes = {
  label: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  options: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    label: PropTypes.string.isRequired,
  })),
  error: PropTypes.string,
  helpText: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  className: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  searchable: PropTypes.bool,
  fontSize: PropTypes.string,
};

export default SearchableSelect;
