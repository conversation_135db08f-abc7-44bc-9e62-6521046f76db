// Design System Theme
export const theme = {
  // Colors
  colors: {
    primary: '#875a7b',
    primaryHover: '#6d4861',
    primaryLight: '#a67b96',
    
    secondary: '#6c757d',
    secondaryHover: '#545b62',
    
    success: '#28a745',
    successHover: '#218838',
    successLight: 'rgba(40, 167, 69, 0.1)',
    
    danger: '#dc3545',
    dangerHover: '#c82333',
    dangerLight: 'rgba(220, 53, 69, 0.1)',
    
    warning: '#ffc107',
    warningHover: '#e0a800',
    warningLight: 'rgba(255, 193, 7, 0.1)',
    
    info: '#17a2b8',
    infoHover: '#138496',
    
    light: '#f8f9fa',
    dark: '#343a40',
    
    white: '#ffffff',
    black: '#000000',
    
    // Text colors
    textPrimary: '#212529',
    textSecondary: '#6c757d',
    textMuted: '#868e96',
    textLight: '#ffffff',
    
    // Background colors
    bgPrimary: '#ffffff',
    bgSecondary: '#f8f9fa',
    bgTertiary: '#e9ecef',
    bgDark: '#343a40',
    
    // Border colors
    border: '#dee2e6',
    borderLight: '#e9ecef',
    borderDark: '#adb5bd',
    
    // Status colors
    online: '#28a745',
    offline: '#6c757d',
    pending: '#ffc107',
    error: '#dc3545',
  },
  
  // Typography
  typography: {
    fontFamily: {
      primary: "'Roboto', 'Lucida Grande', Helvetica, Verdana, Arial, sans-serif",
      monospace: "'Roboto Mono', 'Lucida Console', Monaco, monospace",
      odoo: "'Roboto', 'Lucida Grande', Helvetica, Verdana, Arial, sans-serif",
    },
    fontSize: {
      xs: '11px',       // Odoo small text
      sm: '12px',       // Odoo normal text
      base: '13px',     // Odoo base text
      lg: '14px',       // Odoo large text
      xl: '16px',       // Odoo extra large
      '2xl': '18px',    // Odoo headings
      '3xl': '24px',    // Odoo large headings
      '4xl': '30px',    // Odoo extra large headings
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  
  // Spacing
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
  },
  
  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    full: '9999px',
  },
  
  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
  },
  
  // Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  // Z-index
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modalBackdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
  },
  
  // Transitions
  transitions: {
    fast: '150ms ease-in-out',
    base: '200ms ease-in-out',
    slow: '300ms ease-in-out',
  },
};

// Helper functions
export const getColor = (colorPath) => {
  const keys = colorPath.split('.');
  return keys.reduce((obj, key) => obj[key], theme.colors);
};

export const getSpacing = (size) => theme.spacing[size] || size;
export const getFontSize = (size) => theme.typography.fontSize[size] || size;
export const getBorderRadius = (size) => theme.borderRadius[size] || size;
export const getShadow = (size) => theme.shadows[size] || size;
