import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import { Button, Section, Row, Column, LoadingSpinner } from '../../components/ui';
import { theme } from '../../components/ui/theme';

// Styled Components
const DashboardContainer = styled.div`
  min-height: 100vh;
  background-color: ${theme.colors.bgSecondary};
  padding: ${theme.spacing.lg};
`;

const DashboardHeader = styled.div`
  background-color: ${theme.colors.white};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  margin-bottom: ${theme.spacing.lg};
  box-shadow: ${theme.shadows.sm};
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: ${theme.breakpoints.md}) {
    flex-direction: column;
    gap: ${theme.spacing.lg};
    text-align: center;
  }

  h1 {
    margin: 0;
    color: ${theme.colors.textPrimary};
    font-size: ${theme.typography.fontSize['2xl']};
    font-weight: ${theme.typography.fontWeight.semibold};
  }
`;

const ActionsGroup = styled.div`
  display: flex;
  gap: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    width: 100%;
  }
`;

const StatCard = styled.div`
  background-color: ${theme.colors.white};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  box-shadow: ${theme.shadows.sm};
  border: 1px solid ${theme.colors.border};
  transition: all ${theme.transitions.fast};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.md};
  }
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.md};
`;

const StatIcon = styled.div`
  width: 48px;
  height: 48px;
  background-color: ${props => props.color || theme.colors.primary};
  border-radius: ${theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colors.white};
  font-size: ${theme.typography.fontSize.xl};
`;

const StatTitle = styled.h3`
  margin: 0;
  color: ${theme.colors.textSecondary};
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const StatValue = styled.div`
  font-size: ${theme.typography.fontSize['3xl']};
  font-weight: ${theme.typography.fontWeight.bold};
  color: ${theme.colors.textPrimary};
  font-family: ${theme.typography.fontFamily.monospace};
  margin-bottom: ${theme.spacing.sm};
`;

const StatChange = styled.div.withConfig({
  shouldForwardProp: (prop) => !['positive'].includes(prop),
})`
  font-size: ${theme.typography.fontSize.sm};
  color: ${props => props.positive ? theme.colors.success : theme.colors.danger};
  font-weight: ${theme.typography.fontWeight.medium};

  i {
    margin-right: ${theme.spacing.xs};
  }
`;

const QuickActionsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${theme.spacing.lg};
`;

const QuickActionCard = styled.div`
  background-color: ${theme.colors.white};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.lg};
  box-shadow: ${theme.shadows.sm};
  border: 1px solid ${theme.colors.border};
  cursor: pointer;
  transition: all ${theme.transitions.fast};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.shadows.md};
    border-color: ${theme.colors.primary};
  }

  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
`;

const ActionIcon = styled.div`
  width: 40px;
  height: 40px;
  background-color: ${theme.colors.bgSecondary};
  border-radius: ${theme.borderRadius.base};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colors.primary};
  font-size: ${theme.typography.fontSize.lg};
`;

const ActionContent = styled.div`
  flex: 1;

  h4 {
    margin: 0 0 ${theme.spacing.xs};
    color: ${theme.colors.textPrimary};
    font-size: ${theme.typography.fontSize.base};
    font-weight: ${theme.typography.fontWeight.medium};
  }

  p {
    margin: 0;
    color: ${theme.colors.textMuted};
    font-size: ${theme.typography.fontSize.sm};
  }
`;

const AccountingDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalReceivables: 0,
    totalPayables: 0,
    bankBalance: 0,
    monthlyRevenue: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      console.log('Loading dashboard data from API...');

      const response = await accountingAPI.getDashboardStats();
      console.log('Dashboard API response:', response);

      setStats({
        totalReceivables: response.total_receivables || 0,
        totalPayables: response.total_payables || 0,
        bankBalance: response.bank_balance || 0,
        monthlyRevenue: response.monthly_revenue || 0,
        pendingInvoices: response.pending_invoices || 0,
        overdueInvoices: response.overdue_invoices || 0,
        recentActivities: response.recent_activities || []
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Fallback to mock data if API fails
      setStats({
        totalReceivables: 125000,
        totalPayables: 85000,
        bankBalance: 245000,
        monthlyRevenue: 180000,
        pendingInvoices: 12,
        overdueInvoices: 3,
        recentActivities: []
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const quickActions = [
    {
      title: 'Chart of Accounts',
      icon: 'fas fa-list-alt',
      color: '#28a745',
      action: () => navigate('/accounting/chart-of-accounts')
    },
    {
      title: 'Account Types',
      icon: 'fas fa-tags',
      color: '#6f42c1',
      action: () => navigate('/accounting/account-types')
    },
    {
      title: 'Create Invoice',
      icon: 'fas fa-file-invoice',
      color: '#17a2b8',
      action: () => console.log('Create Invoice')
    },
    {
      title: 'Journal Entries',
      icon: 'fas fa-book',
      color: '#ffc107',
      action: () => navigate('/accounting/journal-entries')
    },
    {
      title: 'Journal Types',
      icon: 'fas fa-layer-group',
      color: '#6f42c1',
      action: () => navigate('/accounting/journal-types')
    },
    {
      title: 'Journals',
      icon: 'fas fa-journal-whills',
      color: '#20c997',
      action: () => navigate('/accounting/journals')
    },
    {
      title: 'Taxes',
      icon: 'fas fa-percentage',
      color: '#17a2b8',
      action: () => navigate('/accounting/taxes')
    },
    {
      title: 'Bank Reconciliation',
      icon: 'fas fa-university',
      color: '#6f42c1',
      action: () => console.log('Bank Reconciliation')
    },
    {
      title: 'Company Settings',
      icon: 'fas fa-cog',
      color: '#dc3545',
      action: () => navigate('/settings/company')
    }
  ];

  const reports = [
    {
      title: 'Profit & Loss',
      description: 'Income statement for current period',
      icon: 'fas fa-chart-line',
      path: '/accounting/reports/profit-loss'
    },
    {
      title: 'Balance Sheet',
      description: 'Financial position statement',
      icon: 'fas fa-balance-scale',
      path: '/accounting/reports/balance-sheet'
    },
    {
      title: 'Cash Flow',
      description: 'Cash flow statement',
      icon: 'fas fa-money-bill-wave',
      path: '/accounting/reports/cash-flow'
    },
    {
      title: 'Aged Receivables',
      description: 'Customer payment analysis',
      icon: 'fas fa-clock',
      path: '/accounting/reports/aged-receivables'
    }
  ];

  if (loading) {
    return (
      <DashboardContainer>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh'
        }}>
          <LoadingSpinner text="Loading accounting data..." size="lg" />
        </div>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      {/* Header */}
      <DashboardHeader>
        <div>
          <h1>
            <i className="fas fa-calculator" style={{ marginRight: theme.spacing.md }}></i>
            Accounting Dashboard
          </h1>
          <p style={{ margin: 0, color: theme.colors.textSecondary }}>
            Financial overview and quick actions
          </p>
        </div>
        <ActionsGroup>
          <Button
            variant="outline"
            color="secondary"
            onClick={() => navigate('/')}
            leftIcon="fas fa-arrow-left"
          >
            Back to Dashboard
          </Button>
          <Button
            color="primary"
            leftIcon="fas fa-plus"
          >
            New Transaction
          </Button>
        </ActionsGroup>
      </DashboardHeader>

      {/* Key Metrics */}
      <Section title="Financial Overview" border>
        <Row columns={4}>
          <Column>
            <StatCard>
              <StatHeader>
                <StatTitle>Total Receivables</StatTitle>
                <StatIcon color={theme.colors.success}>
                  <i className="fas fa-arrow-down"></i>
                </StatIcon>
              </StatHeader>
              <StatValue>{formatCurrency(stats.totalReceivables)}</StatValue>
              <StatChange positive>
                <i className="fas fa-arrow-up"></i>
                +5.2%
              </StatChange>
            </StatCard>
          </Column>

          <Column>
            <StatCard>
              <StatHeader>
                <StatTitle>Total Payables</StatTitle>
                <StatIcon color={theme.colors.danger}>
                  <i className="fas fa-arrow-up"></i>
                </StatIcon>
              </StatHeader>
              <StatValue>{formatCurrency(stats.totalPayables)}</StatValue>
              <StatChange>
                <i className="fas fa-arrow-down"></i>
                -2.1%
              </StatChange>
            </StatCard>
          </Column>

          <Column>
            <StatCard>
              <StatHeader>
                <StatTitle>Bank Balance</StatTitle>
                <StatIcon color={theme.colors.info}>
                  <i className="fas fa-university"></i>
                </StatIcon>
              </StatHeader>
              <StatValue>{formatCurrency(stats.bankBalance)}</StatValue>
              <StatChange positive>
                <i className="fas fa-arrow-up"></i>
                +8.7%
              </StatChange>
            </StatCard>
          </Column>

          <Column>
            <StatCard>
              <StatHeader>
                <StatTitle>Monthly Revenue</StatTitle>
                <StatIcon color={theme.colors.warning}>
                  <i className="fas fa-chart-line"></i>
                </StatIcon>
              </StatHeader>
              <StatValue>{formatCurrency(stats.monthlyRevenue)}</StatValue>
              <StatChange positive>
                <i className="fas fa-arrow-up"></i>
                +12.3%
              </StatChange>
            </StatCard>
          </Column>
        </Row>
      </Section>

      {/* Quick Actions */}
      <Section title="Quick Actions" border>
        <QuickActionsGrid>
          {quickActions.map((action, index) => (
            <QuickActionCard
              key={index}
              onClick={action.action}
            >
              <ActionIcon>
                <i className={action.icon}></i>
              </ActionIcon>
              <ActionContent>
                <h4>{action.title}</h4>
                <p>Click to access</p>
              </ActionContent>
            </QuickActionCard>
          ))}
        </QuickActionsGrid>
      </Section>

      {/* Reports Section */}
      <Section title="Financial Reports" border>
        <QuickActionsGrid>
          {reports.map((report, index) => (
            <QuickActionCard
              key={index}
              onClick={() => navigate(report.path)}
            >
              <ActionIcon>
                <i className={report.icon}></i>
              </ActionIcon>
              <ActionContent>
                <h4>{report.title}</h4>
                <p>{report.description}</p>
              </ActionContent>
            </QuickActionCard>
          ))}
        </QuickActionsGrid>
      </Section>

      {/* Recent Activity */}
      <Section title="Recent Activity" border>
        <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing.md }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.md,
            padding: theme.spacing.md,
            backgroundColor: theme.colors.white,
            borderRadius: theme.borderRadius.base,
            border: `1px solid ${theme.colors.border}`
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: theme.colors.success,
              borderRadius: theme.borderRadius.base,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.colors.white
            }}>
              <i className="fas fa-file-invoice"></i>
            </div>
            <div style={{ flex: 1 }}>
              <h4 style={{ margin: 0, color: theme.colors.textPrimary }}>Invoice INV-2024-001 created</h4>
              <p style={{ margin: 0, color: theme.colors.textSecondary, fontSize: theme.typography.fontSize.sm }}>
                Customer: ABC Corp - Amount: {formatCurrency(15000)}
              </p>
              <span style={{ color: theme.colors.textMuted, fontSize: theme.typography.fontSize.xs }}>
                2 hours ago
              </span>
            </div>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.md,
            padding: theme.spacing.md,
            backgroundColor: theme.colors.white,
            borderRadius: theme.borderRadius.base,
            border: `1px solid ${theme.colors.border}`
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: theme.colors.info,
              borderRadius: theme.borderRadius.base,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.colors.white
            }}>
              <i className="fas fa-credit-card"></i>
            </div>
            <div style={{ flex: 1 }}>
              <h4 style={{ margin: 0, color: theme.colors.textPrimary }}>Payment received</h4>
              <p style={{ margin: 0, color: theme.colors.textSecondary, fontSize: theme.typography.fontSize.sm }}>
                From: XYZ Ltd - Amount: {formatCurrency(8500)}
              </p>
              <span style={{ color: theme.colors.textMuted, fontSize: theme.typography.fontSize.xs }}>
                4 hours ago
              </span>
            </div>
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.md,
            padding: theme.spacing.md,
            backgroundColor: theme.colors.white,
            borderRadius: theme.borderRadius.base,
            border: `1px solid ${theme.colors.border}`
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              backgroundColor: theme.colors.warning,
              borderRadius: theme.borderRadius.base,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.colors.white
            }}>
              <i className="fas fa-edit"></i>
            </div>
            <div style={{ flex: 1 }}>
              <h4 style={{ margin: 0, color: theme.colors.textPrimary }}>Journal entry posted</h4>
              <p style={{ margin: 0, color: theme.colors.textSecondary, fontSize: theme.typography.fontSize.sm }}>
                Entry: JE-2024-045 - Office Expenses
              </p>
              <span style={{ color: theme.colors.textMuted, fontSize: theme.typography.fontSize.xs }}>
                1 day ago
              </span>
            </div>
          </div>
        </div>
      </Section>
    </DashboardContainer>
  );
};

export default AccountingDashboard;
