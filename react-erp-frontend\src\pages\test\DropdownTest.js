import React, { useState } from 'react';
import SearchableSelect from '../../components/ui/SearchableSelect/SearchableSelect';

const DropdownTest = () => {
  const [selectedValue, setSelectedValue] = useState('');

  // Test data
  const testOptions = [
    { value: '', label: 'Select an option...' },
    { value: '1', label: 'Option 1' },
    { value: '2', label: 'Option 2' },
    { value: '3', label: 'Option 3' },
    { value: '4', label: 'Option 4' },
    { value: '5', label: 'Option 5' }
  ];

  const handleChange = (e) => {
    console.log('Dropdown value changed:', e.target.value);
    setSelectedValue(e.target.value);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Dropdown Test Page</h1>
      <p>This page tests the SearchableSelect component with static data.</p>
      
      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#f8f9fa'
      }}>
        <h3>Test SearchableSelect Component</h3>
        <p><strong>Current Value:</strong> {selectedValue || 'None selected'}</p>
        
        <div style={{ marginTop: '20px' }}>
          <SearchableSelect
            label="Test Dropdown"
            name="testDropdown"
            value={selectedValue}
            onChange={handleChange}
            options={testOptions}
            placeholder="Select an option..."
            searchable={true}
            fontSize="14px"
          />
        </div>
        
        <div style={{ marginTop: '20px' }}>
          <h4>Debug Information:</h4>
          <p><strong>Options Count:</strong> {testOptions.length}</p>
          <p><strong>Selected Value:</strong> {selectedValue}</p>
          <p><strong>Selected Label:</strong> {testOptions.find(opt => opt.value === selectedValue)?.label || 'None'}</p>
        </div>
      </div>

      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#fff3cd'
      }}>
        <h3>Instructions</h3>
        <ol>
          <li>Click on the dropdown input field above</li>
          <li>Check if the dropdown list appears</li>
          <li>Try selecting an option</li>
          <li>Check if the value updates correctly</li>
          <li>Try typing to search (if searchable is enabled)</li>
        </ol>
      </div>

      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#e6ffe6'
      }}>
        <h3>Test Results</h3>
        <p>If the dropdown works here but not in the journal entry form, the issue is with data loading or API calls.</p>
        <p>If the dropdown doesn't work here either, the issue is with the SearchableSelect component itself.</p>
      </div>
    </div>
  );
};

export default DropdownTest;
