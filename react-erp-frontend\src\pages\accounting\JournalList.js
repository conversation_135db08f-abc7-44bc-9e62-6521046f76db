import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { accountingAPI } from '../../services/api';
import {
  <PERSON><PERSON><PERSON>r,
  PageHeader,
  PageTitle,
  Button,
  Table,
  LoadingSpinner,
  Alert,
  Badge
} from '../../components/ui';
import { theme } from '../../components/ui/theme';

// Styled Components
const FilterBar = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
  margin-bottom: ${theme.spacing.md};
  align-items: center;
`;

const FilterButton = styled.button`
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: ${props => props.active ? theme.colors.primary : 'white'};
  color: ${props => props.active ? 'white' : theme.colors.textSecondary};
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.15s ease;
  
  &:hover {
    background: ${props => props.active ? theme.colors.primary : '#f8f9fa'};
  }
`;

const TypeBadge = styled(Badge)`
  ${props => {
    const colors = {
      sale: '#28a745',
      purchase: '#dc3545',
      cash: '#ffc107',
      bank: '#007bff',
      general: '#6c757d'
    };
    return `background-color: ${colors[props.type] || '#6c757d'};`;
  }}
`;

const JournalList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const typeFilter = searchParams.get('type');

  const [loading, setLoading] = useState(true);
  const [journals, setJournals] = useState([]);
  const [filteredJournals, setFilteredJournals] = useState([]);
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState(typeFilter || 'all');

  // Journal type definitions
  const JOURNAL_TYPES = {
    all: { name: 'All Types', color: '#6c757d' },
    sale: { name: 'Sales', color: '#28a745' },
    purchase: { name: 'Purchase', color: '#dc3545' },
    cash: { name: 'Cash', color: '#ffc107' },
    bank: { name: 'Bank', color: '#007bff' },
    general: { name: 'Miscellaneous', color: '#6c757d' }
  };

  // Load journals data
  useEffect(() => {
    const loadJournals = async () => {
      try {
        setLoading(true);
        const response = await accountingAPI.getJournals();
        // Handle paginated response from Django REST Framework
        let journalsData = [];
        if (response && response.data) {
          if (response.data.results) {
            // Paginated response structure: { count, next, previous, results }
            journalsData = response.data.results;
          } else if (Array.isArray(response.data)) {
            // Direct array response
            journalsData = response.data;
          } else {
            journalsData = [];
          }
        }

        setJournals(journalsData);
      } catch (err) {
        console.error('Error loading journals:', err);
        setError('Failed to load journals');
      } finally {
        setLoading(false);
      }
    };

    loadJournals();
  }, []);

  // Filter journals based on active filter
  useEffect(() => {
    if (activeFilter === 'all') {
      setFilteredJournals(journals);
    } else {
      setFilteredJournals(journals.filter(journal => journal.type === activeFilter));
    }
  }, [journals, activeFilter]);

  // Set initial filter from URL
  useEffect(() => {
    if (typeFilter && JOURNAL_TYPES[typeFilter]) {
      setActiveFilter(typeFilter);
    }
  }, [typeFilter]);

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
    // Update URL without navigation
    const newSearchParams = new URLSearchParams(searchParams);
    if (filter === 'all') {
      newSearchParams.delete('type');
    } else {
      newSearchParams.set('type', filter);
    }
    window.history.replaceState({}, '', `${window.location.pathname}?${newSearchParams}`);
  };

  const handleCreateJournal = () => {
    const type = activeFilter !== 'all' ? activeFilter : '';
    navigate(`/accounting/journals/create${type ? `?type=${type}` : ''}`);
  };

  const handleEditJournal = (journal) => {
    navigate(`/accounting/journals/${journal.id}/edit`);
  };

  const handleDeleteJournal = async (journal) => {
    if (!window.confirm(`Are you sure you want to delete journal "${journal.name}"?`)) {
      return;
    }

    try {
      await accountingAPI.deleteJournal(journal.id);
      setJournals(journals.filter(j => j.id !== journal.id));
    } catch (err) {
      console.error('Error deleting journal:', err);
      setError('Failed to delete journal');
    }
  };

  // Table columns
  const columns = [
    {
      key: 'code',
      title: 'Code',
      width: '100px',
      render: (value) => (
        <span style={{ fontWeight: 600, fontFamily: 'monospace' }}>
          {value}
        </span>
      )
    },
    {
      key: 'name',
      title: 'Journal Name',
      width: '250px'
    },
    {
      key: 'type',
      title: 'Type',
      width: '120px',
      render: (value) => (
        <TypeBadge type={value}>
          {JOURNAL_TYPES[value]?.name || value}
        </TypeBadge>
      )
    },
    {
      key: 'default_account_name',
      title: 'Default Account',
      width: '200px'
    },
    {
      key: 'currency_name',
      title: 'Currency',
      width: '100px'
    },
    {
      key: 'active',
      title: 'Status',
      width: '80px',
      render: (value) => (
        <Badge color={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      width: '120px',
      render: (_, journal) => (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleEditJournal(journal)}
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="outline"
            color="danger"
            onClick={() => handleDeleteJournal(journal)}
          >
            Delete
          </Button>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <div className="header-content">
          <PageTitle>
            Journals
            {activeFilter !== 'all' && (
              <span style={{ color: theme.colors.textSecondary, fontWeight: 400 }}>
                {' '}- {JOURNAL_TYPES[activeFilter]?.name}
              </span>
            )}
          </PageTitle>
        </div>
        <div className="header-actions">
          <Button
            variant="outline"
            onClick={() => navigate('/accounting/journal-types')}
          >
            Journal Types
          </Button>
          <Button
            color="primary"
            onClick={handleCreateJournal}
          >
            Create Journal
          </Button>
        </div>
      </PageHeader>

      {error && (
        <Alert type="error" style={{ marginBottom: theme.spacing.md }}>
          {error}
        </Alert>
      )}

      {/* Filter Bar */}
      <FilterBar>
        <span style={{ fontSize: '14px', color: theme.colors.textSecondary }}>
          Filter by type:
        </span>
        {Object.entries(JOURNAL_TYPES).map(([key, type]) => (
          <FilterButton
            key={key}
            active={activeFilter === key}
            onClick={() => handleFilterChange(key)}
          >
            {type.name}
            {key !== 'all' && (
              <span style={{ marginLeft: '4px', opacity: 0.7 }}>
                ({journals.filter(j => j.type === key).length})
              </span>
            )}
          </FilterButton>
        ))}
      </FilterBar>

      {/* Journals Table */}
      <Table
        columns={columns}
        data={filteredJournals}
        empty={
          <div style={{
            textAlign: 'center',
            padding: '40px 20px',
            color: '#6c757d'
          }}>
            <i className="fas fa-book" style={{ fontSize: '48px', marginBottom: '16px' }} />
            <h3>
              {activeFilter === 'all'
                ? "No journals found"
                : `No ${JOURNAL_TYPES[activeFilter]?.name.toLowerCase()} journals found`}
            </h3>
            <p>
              {activeFilter === 'all'
                ? "Create your first journal to get started."
                : `Create a ${JOURNAL_TYPES[activeFilter]?.name.toLowerCase()} journal to get started.`}
            </p>
          </div>
        }
      />


      {/* Summary */}
      {filteredJournals.length > 0 && (
        <div style={{ 
          marginTop: theme.spacing.md, 
          padding: theme.spacing.sm,
          background: '#f8f9fa',
          borderRadius: '4px',
          fontSize: '14px',
          color: theme.colors.textSecondary
        }}>
          Showing {filteredJournals.length} of {journals.length} journals
          {activeFilter !== 'all' && ` (${JOURNAL_TYPES[activeFilter]?.name} type)`}
        </div>
      )}
    </PageContainer>
  );
};

export default JournalList;
