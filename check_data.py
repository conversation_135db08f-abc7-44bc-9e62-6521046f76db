#!/usr/bin/env python
"""
Check what data exists in the database
"""
import requests
import json

# API Configuration
BASE_URL = 'http://localhost:8000/api/v1'

def check_data():
    print("🔍 Checking existing data...")
    
    endpoints = [
        ('/companies/', 'Companies'),
        ('/currencies/', 'Currencies'),
        ('/partners/', 'Partners'),
        ('/accounting/journals/', 'Journals'),
        ('/accounting/accounts/', 'Accounts'),
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f'{BASE_URL}{endpoint}')
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    count = len(data['results'])
                    items = data['results'][:3]  # Show first 3 items
                elif isinstance(data, list):
                    count = len(data)
                    items = data[:3]  # Show first 3 items
                else:
                    count = 1
                    items = [data]
                
                print(f"\n✅ {name}: {count} records")
                for item in items:
                    if 'name' in item:
                        print(f"   - {item.get('id', '?')}: {item['name']}")
                    elif 'code' in item:
                        print(f"   - {item.get('id', '?')}: {item['code']}")
                    else:
                        print(f"   - {item.get('id', '?')}: {str(item)[:50]}...")
                        
            elif response.status_code == 401:
                print(f"❌ {name}: Authentication required")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Cannot connect to server")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

if __name__ == "__main__":
    check_data()
