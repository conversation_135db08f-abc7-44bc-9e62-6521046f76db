#!/usr/bin/env python
"""
Test login functionality
"""
import requests
import json

def test_login():
    url = 'http://localhost:8000/api/v1/auth/login/'
    data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    try:
        print("🔐 Testing login...")
        print(f"URL: {url}")
        print(f"Data: {data}")
        
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"User: {result.get('user', {}).get('username', 'N/A')}")
            print(f"Access Token: {result.get('access', 'N/A')[:50]}...")
        else:
            print("❌ Login failed!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server. Is it running on port 8000?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_login()
