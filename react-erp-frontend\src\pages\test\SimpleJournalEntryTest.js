import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>ontainer,
  PageHeader,
  PageTitle,
  Button,
  Section,
  Row,
  Column,
  Input,
  SearchableSelect,
  Form,
  Actions,
  Alert,
  LoadingSpinner
} from '../../components/ui';
import { accountingAPI, coreAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const SimpleJournalEntryTest = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [journals, setJournals] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [partners, setPartners] = useState([]);
  
  const [formData, setFormData] = useState({
    ref: '',
    date: new Date().toISOString().split('T')[0],
    journal: '',
    partner: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError('');
    
    try {
      console.log('Loading data for simple journal entry test...');
      console.log('Auth status:', { isAuthenticated, user: user?.username });
      
      if (!isAuthenticated) {
        setError('You are not authenticated. Please log in first.');
        setLoading(false);
        return;
      }

      // Load all required data
      const [journalsResponse, accountsResponse, partnersResponse] = await Promise.all([
        accountingAPI.getJournals().catch(err => {
          console.error('Failed to load journals:', err);
          return { data: [] };
        }),
        accountingAPI.getAccounts().catch(err => {
          console.error('Failed to load accounts:', err);
          return { data: [] };
        }),
        coreAPI.getPartners().catch(err => {
          console.error('Failed to load partners:', err);
          return { data: [] };
        })
      ]);

      console.log('API Responses:', {
        journals: journalsResponse,
        accounts: accountsResponse,
        partners: partnersResponse
      });

      const journalsData = journalsResponse?.data || [];
      const accountsData = accountsResponse?.data || [];
      const partnersData = partnersResponse?.data || [];

      setJournals(journalsData);
      setAccounts(accountsData);
      setPartners(partnersData);
      
      console.log('Data loaded successfully:', {
        journals: journalsData.length,
        accounts: accountsData.length,
        partners: partnersData.length
      });

    } catch (err) {
      console.error('Error loading data:', err);
      setError(`Failed to load data: ${err.message}`);
      setJournals([]);
      setAccounts([]);
      setPartners([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    alert('Form submitted successfully! Check console for details.');
  };

  if (loading) {
    return (
      <PageContainer>
        <PageHeader>
          <PageTitle>Simple Journal Entry Test</PageTitle>
        </PageHeader>
        <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
          <LoadingSpinner />
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <div className="header-content">
          <PageTitle>Simple Journal Entry Test</PageTitle>
          <p className="subtitle">Testing basic form functionality without complex components</p>
        </div>
        <div className="header-actions">
          <Button
            variant="outline"
            color="secondary"
            onClick={() => navigate('/accounting/journal-entries')}
          >
            Back to List
          </Button>
        </div>
      </PageHeader>

      {error && (
        <Alert type="error" style={{ marginBottom: '20px' }}>
          {error}
        </Alert>
      )}

      <Section border>
        <Form onSubmit={handleSubmit}>
          <Row columns={2}>
            <Column>
              <Input
                label="Reference"
                name="ref"
                value={formData.ref}
                onChange={(e) => handleInputChange('ref', e.target.value)}
                placeholder="Enter reference"
              />
            </Column>
            <Column>
              <Input
                label="Date"
                name="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </Column>
          </Row>
          
          <Row columns={2}>
            <Column>
              <SearchableSelect
                label="Journal"
                name="journal"
                value={formData.journal}
                onChange={(value) => handleInputChange('journal', value)}
                options={[
                  { value: '', label: 'Select journal...' },
                  ...(Array.isArray(journals) && journals.length > 0 ? journals.map(journal => ({
                    value: journal.id,
                    label: `${journal.code || journal.id} - ${journal.name || 'Unnamed Journal'}`
                  })) : [])
                ]}
                required
                searchable={true}
              />
            </Column>
            <Column>
              <SearchableSelect
                label="Partner"
                name="partner"
                value={formData.partner}
                onChange={(value) => handleInputChange('partner', value)}
                options={[
                  { value: '', label: 'Select partner...' },
                  ...(Array.isArray(partners) && partners.length > 0 ? partners.map(partner => ({
                    value: partner.id,
                    label: partner.name || `Partner ${partner.id}`
                  })) : [])
                ]}
                searchable={true}
              />
            </Column>
          </Row>

          <Actions>
            <Button type="button" variant="secondary" onClick={() => setFormData({
              ref: '',
              date: new Date().toISOString().split('T')[0],
              journal: '',
              partner: ''
            })}>
              Clear Form
            </Button>
            <Button type="submit" variant="primary">
              Test Submit
            </Button>
          </Actions>
        </Form>
      </Section>

      <Section border>
        <h3>Debug Information</h3>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          <div><strong>Authentication:</strong> {isAuthenticated ? 'Yes' : 'No'}</div>
          <div><strong>User:</strong> {user?.username || 'None'}</div>
          <div><strong>Journals loaded:</strong> {journals.length}</div>
          <div><strong>Accounts loaded:</strong> {accounts.length}</div>
          <div><strong>Partners loaded:</strong> {partners.length}</div>
          <div><strong>Current form data:</strong></div>
          <pre>{JSON.stringify(formData, null, 2)}</pre>
        </div>
      </Section>
    </PageContainer>
  );
};

export default SimpleJournalEntryTest;
