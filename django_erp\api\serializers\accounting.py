from rest_framework import serializers
from accounting.models import (
    AccountGroup, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountPayment, AccountTax, AccountType
)
from core.models import Company, Partner, Currency


class AccountGroupSerializer(serializers.ModelSerializer):
    """Serializer for Account Groups"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    parent_name = serializers.CharField(source='parent.name', read_only=True)

    class Meta:
        model = AccountGroup
        fields = ['id', 'name', 'code_prefix_start', 'code_prefix_end', 'parent',
                 'company', 'company_name', 'parent_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'company', 'company_name', 'parent_name', 'create_date', 'write_date']


class AccountAccountSerializer(serializers.ModelSerializer):
    """Serializer for Chart of Accounts"""
    group_name = serializers.Cha<PERSON><PERSON><PERSON>(source='group.name', read_only=True)
    company_name = serializers.Char<PERSON>ield(source='company.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    balance = serializers.SerializerMethodField()
    active = serializers.SerializerMethodField()
    opening_balance = serializers.DecimalField(max_digits=20, decimal_places=2, required=False, allow_null=True, write_only=True)

    class Meta:
        model = AccountAccount
        fields = ['id', 'name', 'code', 'account_type', 'internal_group', 'reconcile', 'deprecated', 'active',
                 'group', 'group_name', 'company', 'company_name',
                 'currency', 'currency_name', 'note', 'balance', 'opening_balance', 'create_date', 'write_date']
        read_only_fields = ['id', 'internal_group', 'active', 'company', 'company_name', 'balance', 'create_date', 'write_date']

    def get_active(self, obj):
        """Convert deprecated field to active for frontend compatibility"""
        return not obj.deprecated
    
    def get_balance(self, obj):
        """Calculate account balance from move lines"""
        # TODO: Implement balance calculation from AccountMoveLine
        # For now, return a mock balance based on account type
        if obj.account_type in ['asset_cash', 'asset_receivable', 'asset_current', 'asset_fixed']:
            return 25000.00  # Mock positive balance for assets
        elif obj.account_type in ['liability_payable', 'liability_current', 'liability_non_current']:
            return -15000.00  # Mock negative balance for liabilities
        elif obj.account_type == 'income':
            return -50000.00  # Mock negative balance for income
        elif obj.account_type in ['expense', 'expense_direct_cost']:
            return 30000.00  # Mock positive balance for expenses
        else:
            return 0.00

    def update(self, instance, validated_data):
        """Override update to handle account type changes with validation"""
        # Check if account_type is being changed
        if 'account_type' in validated_data and instance.account_type != validated_data['account_type']:
            # Get change restrictions
            restrictions = instance.can_change_account_type()

            # If there are errors, raise validation error
            if restrictions['errors']:
                raise serializers.ValidationError({
                    'account_type': restrictions['errors']
                })

        return super().update(instance, validated_data)


class AccountTypeSerializer(serializers.ModelSerializer):
    """Serializer for AccountType model"""

    usage_info = serializers.SerializerMethodField()
    accounts_count = serializers.SerializerMethodField()
    can_delete = serializers.SerializerMethodField()
    can_modify = serializers.SerializerMethodField()

    class Meta:
        model = AccountType
        fields = [
            'id', 'code', 'name', 'internal_group', 'description',
            'requires_reconciliation', 'is_system_type', 'active', 'sequence',
            'usage_info', 'accounts_count', 'can_delete', 'can_modify',
            'create_date', 'write_date'
        ]
        read_only_fields = ['id', 'create_date', 'write_date', 'usage_info', 'accounts_count', 'can_delete', 'can_modify']

    def get_usage_info(self, obj):
        """Get usage information for this account type"""
        return obj.get_usage_info()

    def get_accounts_count(self, obj):
        """Get count of accounts using this type"""
        from accounting.models import AccountAccount
        return AccountAccount.objects.filter(account_type=obj.code).count()

    def get_can_delete(self, obj):
        """Check if this account type can be deleted"""
        can_delete, reason = obj.can_be_deleted()
        return {
            'allowed': can_delete,
            'reason': reason
        }

    def get_can_modify(self, obj):
        """Get modification restrictions for this account type"""
        return obj.can_be_modified()

    def validate_code(self, value):
        """Validate account type code"""
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', value):
            raise serializers.ValidationError("Account type code can only contain alphanumeric characters and underscores.")
        return value

    def validate(self, data):
        """Cross-field validation"""
        # Check for duplicate codes (excluding current instance during updates)
        code = data.get('code')
        if code:
            queryset = AccountType.objects.filter(code=code)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise serializers.ValidationError({'code': 'Account type with this code already exists.'})

        return data


class AccountJournalSerializer(serializers.ModelSerializer):
    """Serializer for Account Journals"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    default_account_name = serializers.CharField(source='default_account.name', read_only=True)
    default_account_code = serializers.CharField(source='default_account.code', read_only=True)
    suspense_account_name = serializers.CharField(source='suspense_account.name', read_only=True)
    suspense_account_code = serializers.CharField(source='suspense_account.code', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    currency_symbol = serializers.CharField(source='currency.symbol', read_only=True)

    class Meta:
        model = AccountJournal
        fields = ['id', 'name', 'code', 'type', 'active', 'sequence',
                 'default_account', 'default_account_name', 'default_account_code',
                 'suspense_account', 'suspense_account_name', 'suspense_account_code',
                 'company', 'company_name', 'currency', 'currency_name', 'currency_symbol',
                 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']

    def validate_code(self, value):
        """Validate journal code"""
        if not value:
            raise serializers.ValidationError("Journal code is required.")

        # Convert to uppercase for consistency
        value = value.upper()

        # Check for alphanumeric characters only
        import re
        if not re.match(r'^[A-Z0-9]+$', value):
            raise serializers.ValidationError("Journal code can only contain uppercase letters and numbers.")

        return value

    def validate(self, data):
        """Cross-field validation"""
        # Check for duplicate codes within the same company
        code = data.get('code')
        company = data.get('company')

        if code and company:
            code = code.upper()  # Ensure uppercase
            queryset = AccountJournal.objects.filter(code=code, company=company)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise serializers.ValidationError({
                    'code': f'Journal with code "{code}" already exists for this company.'
                })

        # Validate default account type for journal type
        journal_type = data.get('type')
        default_account = data.get('default_account')

        if journal_type and default_account:
            if journal_type == 'sale' and not default_account.account_type.startswith('income'):
                raise serializers.ValidationError({
                    'default_account': 'Sales journals must use income accounts as default account.'
                })
            elif journal_type == 'purchase' and not default_account.account_type.startswith('expense'):
                raise serializers.ValidationError({
                    'default_account': 'Purchase journals must use expense accounts as default account.'
                })

        return data


class AccountMoveLineSerializer(serializers.ModelSerializer):
    """Serializer for Account Move Lines (Journal Entry Lines)"""
    # Related field names
    account_name = serializers.CharField(source='account.name', read_only=True)
    account_code = serializers.CharField(source='account.code', read_only=True)
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    tax_line_name = serializers.CharField(source='tax_line.name', read_only=True)

    # Computed fields
    display_name = serializers.SerializerMethodField()
    amount_total = serializers.SerializerMethodField()

    def get_display_name(self, obj):
        """Get display name for the line"""
        return f"{obj.account.code} - {obj.name}" if obj.account else obj.name

    def get_amount_total(self, obj):
        """Get total amount (debit or credit)"""
        return obj.debit if obj.debit > 0 else obj.credit

    class Meta:
        model = AccountMoveLine
        fields = [
            # Basic information
            'id', 'name', 'ref', 'display_name',
            # Account and partner
            'account', 'account_name', 'account_code',
            'partner', 'partner_name',
            # Amounts
            'debit', 'credit', 'balance', 'amount_total',
            # Currency
            'currency', 'currency_name', 'amount_currency',
            # Dates
            'date', 'date_maturity',
            # Tax information
            'taxes', 'tax_line', 'tax_line_name', 'tax_base_amount',
            # Reconciliation
            'reconciled', 'full_reconcile', 'matching_number',
            # Product information
            'quantity', 'price_unit', 'discount',
            # Sequence
            'sequence',
            # System fields
            'create_date', 'write_date'
        ]
        read_only_fields = ['id', 'balance', 'display_name', 'amount_total', 'create_date', 'write_date']


class AccountMoveSerializer(serializers.ModelSerializer):
    """Serializer for Account Moves (Journal Entries)"""
    # Related field names
    journal_name = serializers.CharField(source='journal.name', read_only=True)
    journal_code = serializers.CharField(source='journal.code', read_only=True)
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)

    # Line items
    line_ids = AccountMoveLineSerializer(many=True, required=False)

    # Computed fields
    display_name = serializers.SerializerMethodField()
    state_display = serializers.CharField(source='get_state_display', read_only=True)
    move_type_display = serializers.CharField(source='get_move_type_display', read_only=True)
    payment_state_display = serializers.CharField(source='get_payment_state_display', read_only=True)
    is_balanced = serializers.SerializerMethodField()
    total_debit = serializers.SerializerMethodField()
    total_credit = serializers.SerializerMethodField()

    def get_display_name(self, obj):
        """Get display name for the move"""
        return obj.name or f"Draft Entry - {obj.date}"

    def get_is_balanced(self, obj):
        """Check if the move is balanced (safe for serialization)"""
        try:
            # Use the safe balance check method that doesn't raise exceptions
            return obj._check_balanced() if hasattr(obj, '_check_balanced') else True
        except Exception:
            # If there's any error, return False but don't break serialization
            return False

    def get_total_debit(self, obj):
        """Get total debit amount"""
        return sum(line.debit for line in obj.line_ids.all())

    def get_total_credit(self, obj):
        """Get total credit amount"""
        return sum(line.credit for line in obj.line_ids.all())

    def create(self, validated_data):
        """Create AccountMove with nested line_ids"""
        line_ids_data = validated_data.pop('line_ids', [])
        move = AccountMove.objects.create(**validated_data)

        # Create move lines with proper user context
        for line_data in line_ids_data:
            # Set create_uid and write_uid for line items from request context
            if hasattr(self, 'context') and 'request' in self.context:
                user = self.context['request'].user
                line_data['create_uid'] = user
                line_data['write_uid'] = user
            AccountMoveLine.objects.create(move=move, **line_data)

        return move

    def update(self, instance, validated_data):
        """Update AccountMove with nested line_ids"""
        line_ids_data = validated_data.pop('line_ids', None)

        # Update move fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update move lines if provided
        if line_ids_data is not None:
            # Delete existing lines
            instance.line_ids.all().delete()
            # Create new lines with proper user context
            for line_data in line_ids_data:
                # Set create_uid and write_uid for line items from request context
                if hasattr(self, 'context') and 'request' in self.context:
                    user = self.context['request'].user
                    line_data['create_uid'] = user
                    line_data['write_uid'] = user
                AccountMoveLine.objects.create(move=instance, **line_data)

        return instance

    class Meta:
        model = AccountMove
        fields = [
            # Basic information
            'id', 'name', 'ref', 'date', 'display_name',
            # Type and state
            'move_type', 'move_type_display', 'state', 'state_display',
            # Journal and company
            'journal', 'journal_name', 'journal_code',
            'company', 'company_name',
            'currency', 'currency_name',
            # Partner information
            'partner', 'partner_name',
            # Invoice specific fields
            'invoice_date', 'invoice_date_due', 'payment_reference',
            'payment_state', 'payment_state_display',
            # Amounts
            'amount_untaxed', 'amount_tax', 'amount_total', 'amount_residual',
            'total_debit', 'total_credit', 'is_balanced',
            # Control fields
            'posted_before', 'to_check',
            # Hash and security
            'inalterable_hash', 'secure_sequence_number',
            # Advanced fields
            'invoice_origin', 'invoice_source_email',
            # Line items
            'line_ids',
            # System fields
            'create_date', 'write_date'
        ]
        read_only_fields = [
            'id', 'name', 'display_name', 'state_display', 'move_type_display',
            'payment_state_display', 'amount_total', 'amount_residual', 'is_balanced',
            'total_debit', 'total_credit', 'posted_before', 'inalterable_hash',
            'secure_sequence_number', 'create_date', 'write_date'
        ]


class AccountPaymentSerializer(serializers.ModelSerializer):
    """Serializer for Account Payments"""
    partner_name = serializers.CharField(source='partner.name', read_only=True)
    journal_name = serializers.CharField(source='journal.name', read_only=True)
    company_name = serializers.CharField(source='company.name', read_only=True)
    currency_name = serializers.CharField(source='currency.name', read_only=True)
    
    class Meta:
        model = AccountPayment
        fields = ['id', 'name', 'payment_type', 'partner_type', 'amount',
                 'currency', 'currency_name', 'date', 'ref', 'state',
                 'partner', 'partner_name', 'journal', 'journal_name',
                 'company', 'company_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'name', 'create_date', 'write_date']


class AccountTaxSerializer(serializers.ModelSerializer):
    """Serializer for Account Taxes"""
    company_name = serializers.CharField(source='company.name', read_only=True)
    tax_payable_account_name = serializers.CharField(source='tax_payable_account.name', read_only=True)
    tax_receivable_account_name = serializers.CharField(source='tax_receivable_account.name', read_only=True)

    class Meta:
        model = AccountTax
        fields = ['id', 'name', 'description', 'amount', 'amount_type', 'type_tax_use', 'tax_scope',
                 'active', 'sequence', 'price_include', 'is_withholding', 'withholding_type',
                 'tax_payable_account', 'tax_payable_account_name',
                 'tax_receivable_account', 'tax_receivable_account_name',
                 'company', 'company_name', 'create_date', 'write_date']
        read_only_fields = ['id', 'create_date', 'write_date']


# Dashboard Statistics Serializer
class AccountingDashboardSerializer(serializers.Serializer):
    """Serializer for accounting dashboard statistics"""
    total_receivables = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_payables = serializers.DecimalField(max_digits=16, decimal_places=2)
    bank_balance = serializers.DecimalField(max_digits=16, decimal_places=2)
    monthly_revenue = serializers.DecimalField(max_digits=16, decimal_places=2)
    pending_invoices = serializers.IntegerField()
    overdue_invoices = serializers.IntegerField()
    
    # Recent activities
    recent_activities = serializers.ListField(
        child=serializers.DictField(), 
        read_only=True
    )
