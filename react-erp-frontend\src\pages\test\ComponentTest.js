import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>r,
  PageHeader,
  PageTitle,
  Button,
  Section,
  Row,
  Column,
  Input,
  SearchableSelect,
  Form,
  Actions,
  Alert
} from '../../components/ui';

const ComponentTest = () => {
  const [testValue, setTestValue] = useState('');
  const [selectValue, setSelectValue] = useState('');
  const [showAlert, setShowAlert] = useState(false);

  const testOptions = [
    { value: '1', label: 'Option 1' },
    { value: '2', label: 'Option 2' },
    { value: '3', label: 'Option 3' }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  return (
    <PageContainer>
      <PageHeader>
        <div className="header-content">
          <PageTitle>Component Test Page</PageTitle>
          <p className="subtitle">Testing all UI components used in Journal Entry Form</p>
        </div>
      </PageHeader>

      {showAlert && (
        <Alert type="success">
          Form submitted successfully! All components are working.
        </Alert>
      )}

      <Section border>
        <h3>Basic Components Test</h3>
        
        <Form onSubmit={handleSubmit}>
          <Row columns={3}>
            <Column>
              <label>Input Component:</label>
              <Input
                value={testValue}
                onChange={(e) => setTestValue(e.target.value)}
                placeholder="Test input..."
              />
            </Column>
            
            <Column>
              <label>SearchableSelect Component:</label>
              <SearchableSelect
                value={selectValue}
                onChange={setSelectValue}
                options={testOptions}
                placeholder="Select an option..."
              />
            </Column>
            
            <Column>
              <label>Button Component:</label>
              <Button type="submit" variant="primary">
                Test Submit
              </Button>
            </Column>
          </Row>
          
          <Actions>
            <Button type="button" variant="secondary" onClick={() => setTestValue('')}>
              Clear Input
            </Button>
            <Button type="button" variant="secondary" onClick={() => setSelectValue('')}>
              Clear Select
            </Button>
            <Button type="submit" variant="primary">
              Submit Test
            </Button>
          </Actions>
        </Form>
      </Section>

      <Section border>
        <h3>Component Status</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '16px',
          marginTop: '16px'
        }}>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>PageContainer:</strong> ✅ Working
          </div>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>PageHeader:</strong> ✅ Working
          </div>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>Form:</strong> ✅ Working
          </div>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>Input:</strong> ✅ Working
          </div>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>SearchableSelect:</strong> {selectValue ? '✅ Working' : '⏳ Test it'}
          </div>
          <div style={{ 
            padding: '12px', 
            border: '1px solid #ddd', 
            borderRadius: '4px',
            backgroundColor: '#f8f9fa'
          }}>
            <strong>Button:</strong> ✅ Working
          </div>
        </div>
      </Section>

      <Section border>
        <h3>Current Values</h3>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f5f5f5', 
          borderRadius: '4px',
          fontFamily: 'monospace'
        }}>
          <div><strong>Input Value:</strong> "{testValue}"</div>
          <div><strong>Select Value:</strong> "{selectValue}"</div>
          <div><strong>Selected Option:</strong> {
            testOptions.find(opt => opt.value === selectValue)?.label || 'None'
          }</div>
        </div>
      </Section>

      <Section border>
        <h3>Instructions</h3>
        <ol>
          <li>Test the input field by typing something</li>
          <li>Test the dropdown by selecting an option</li>
          <li>Click the submit button to test form submission</li>
          <li>Check browser console for any errors</li>
          <li>If all components work here, the issue is specific to Journal Entry Form</li>
        </ol>
      </Section>
    </PageContainer>
  );
};

export default ComponentTest;
