#!/usr/bin/env python
"""
Reset admin password
"""
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.contrib.auth.models import User

def reset_admin_password():
    try:
        user = User.objects.get(username='admin')
        user.set_password('admin')
        user.save()
        print(f"✅ Password reset for user: {user.username}")
        print(f"   Username: admin")
        print(f"   Password: admin")
        print(f"   Email: {user.email}")
        print(f"   Is staff: {user.is_staff}")
        print(f"   Is superuser: {user.is_superuser}")
    except User.DoesNotExist:
        print("❌ Admin user not found")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    reset_admin_password()
