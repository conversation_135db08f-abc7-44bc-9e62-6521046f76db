<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testJournals()">Test Journals API</button>
    <button onclick="testAccounts()">Test Accounts API</button>
    <button onclick="testPartners()">Test Partners API</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/v1';
        
        async function testJournals() {
            try {
                const response = await fetch(`${API_BASE}/accounting/journals/`);
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Journals:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Journals Error:</h3><pre>' + error.message + '</pre>';
            }
        }
        
        async function testAccounts() {
            try {
                const response = await fetch(`${API_BASE}/accounting/accounts/`);
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Accounts:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Accounts Error:</h3><pre>' + error.message + '</pre>';
            }
        }
        
        async function testPartners() {
            try {
                const response = await fetch(`${API_BASE}/core/partners/`);
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Partners:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Partners Error:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
