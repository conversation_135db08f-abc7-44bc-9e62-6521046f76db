import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

const AuthTest = () => {
  const { user, isAuthenticated, login, logout } = useAuth();

  const handleTestLogin = async () => {
    try {
      // Try to login with test credentials
      await login('admin', 'admin123'); // Adjust credentials as needed
      console.log('Login successful');
    } catch (error) {
      console.error('Lo<PERSON> failed:', error);
    }
  };

  const handleLogout = () => {
    logout();
    console.log('Logged out');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Authentication Test Page</h1>
      
      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: isAuthenticated ? '#e6ffe6' : '#ffe6e6'
      }}>
        <h3>Authentication Status</h3>
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        <p><strong>User:</strong> {user ? user.username : 'Not logged in'}</p>
        <p><strong>User ID:</strong> {user ? user.id : 'N/A'}</p>
        <p><strong>Access Token:</strong> {localStorage.getItem('access_token') ? 'Present' : 'Missing'}</p>
        <p><strong>Refresh Token:</strong> {localStorage.getItem('refresh_token') ? 'Present' : 'Missing'}</p>
      </div>

      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#f8f9fa'
      }}>
        <h3>Actions</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {!isAuthenticated && (
            <button 
              onClick={handleTestLogin}
              style={{
                padding: '10px 20px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Test Login (admin/admin123)
            </button>
          )}
          
          {isAuthenticated && (
            <button 
              onClick={handleLogout}
              style={{
                padding: '10px 20px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Logout
            </button>
          )}
          
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh Page
          </button>
        </div>
      </div>

      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#fff3cd'
      }}>
        <h3>Instructions</h3>
        <ol>
          <li>Check if you're currently authenticated</li>
          <li>If not authenticated, try the test login button</li>
          <li>If login fails, go to the login page manually</li>
          <li>Once authenticated, go back to the journal entry form</li>
          <li>Check if the dropdowns work now</li>
        </ol>
      </div>

      <div style={{ 
        margin: '20px 0', 
        padding: '20px', 
        border: '1px solid #ddd', 
        borderRadius: '5px',
        backgroundColor: '#e2e3e5'
      }}>
        <h3>Debug Information</h3>
        <p><strong>Current URL:</strong> {window.location.href}</p>
        <p><strong>Local Storage Keys:</strong> {Object.keys(localStorage).join(', ')}</p>
        <p><strong>User Object:</strong></p>
        <pre style={{ 
          background: '#f5f5f5', 
          padding: '10px', 
          overflow: 'auto',
          fontSize: '12px'
        }}>
          {JSON.stringify(user, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default AuthTest;
