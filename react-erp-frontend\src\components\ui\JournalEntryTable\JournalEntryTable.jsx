import React, { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { theme } from '../theme';
import SearchableSelect from '../SearchableSelect/SearchableSelect';
import CurrencyInput from '../CurrencyInput/CurrencyInput';
import Input from '../Input/Input';
// import { TaxSelector } from '../../business'; // Using SearchableSelect instead

const TableContainer = styled.div`
  border: 1px solid #dee2e6;
  border-radius: 3px;
  overflow: hidden;
  background: white;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: 13px;
`;

const TableHeader = styled.thead`
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
`;

const HeaderRow = styled.tr``;

const HeaderCell = styled.th.withConfig({
  shouldForwardProp: (prop) => !['width', 'minWidth'].includes(prop),
})`
  padding: 8px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-right: 1px solid #dee2e6;
  position: relative;
  user-select: none;

  &:last-child {
    border-right: none;
  }

  ${props => props.width && `width: ${props.width};`}
  ${props => props.minWidth && `min-width: ${props.minWidth};`}
`;

const ResizeHandle = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  
  &:hover {
    background: #875a7b;
  }
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid #f8f9fa;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const TableCell = styled.td.withConfig({
  shouldForwardProp: (prop) => !['width', 'minWidth'].includes(prop),
})`
  padding: 4px 8px;
  border-right: 1px solid #f8f9fa;
  vertical-align: middle;

  &:last-child {
    border-right: none;
  }

  ${props => props.width && `width: ${props.width};`}
  ${props => props.minWidth && `min-width: ${props.minWidth};`}
`;

const ActionCell = styled(TableCell)`
  width: 60px;
  min-width: 60px;
  text-align: center;
  padding: 4px;
`;

const RemoveButton = styled.button`
  background: none;
  border: 1px solid #dc3545;
  color: #dc3545;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  font-family: ${theme.typography.fontFamily.odoo};
  
  &:hover {
    background-color: #dc3545;
    color: white;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AddLineButton = styled.button`
  background: none;
  border: 1px solid #875a7b;
  color: #875a7b;
  padding: 6px 12px;
  border-radius: 3px;
  font-size: 13px;
  cursor: pointer;
  font-family: ${theme.typography.fontFamily.odoo};
  margin: 8px 0;
  
  &:hover {
    background-color: #875a7b;
    color: white;
  }
`;

const JournalEntryTable = ({
  lines = [],
  onLinesChange,
  accounts = [],
  partners = [],
  taxes = [],
  onAddLine,
  onRemoveLine,
  showSalesTax = false,
  showDate = false,
  journalType = 'general',
  className = ''
}) => {
  const [columnWidths, setColumnWidths] = useState({
    date: '120px',
    account: '200px',
    description: '200px',
    partner: '150px',
    debit: '120px',
    credit: '120px',
    salesTax: '120px',
    action: '60px'
  });
  
  const resizingRef = useRef(null);

  const handleLineChange = (index, field, value) => {
    const updatedLines = [...lines];
    updatedLines[index] = {
      ...updatedLines[index],
      [field]: value
    };

    // Clear opposite field for debit/credit
    if (field === 'debit' && value) {
      updatedLines[index].credit = '';
    } else if (field === 'credit' && value) {
      updatedLines[index].debit = '';
    }

    onLinesChange(updatedLines);
  };

  // Wrapper functions for SearchableSelect onChange
  const handleAccountChange = (index) => (e) => {
    handleLineChange(index, 'account', e.target.value);
  };

  const handlePartnerChange = (index) => (e) => {
    handleLineChange(index, 'partner', e.target.value);
  };

  const handleMouseDown = (e, column) => {
    e.preventDefault();
    resizingRef.current = {
      column,
      startX: e.clientX,
      startWidth: parseInt(columnWidths[column])
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (!resizingRef.current) return;
    
    const { column, startX, startWidth } = resizingRef.current;
    const diff = e.clientX - startX;
    const newWidth = Math.max(80, startWidth + diff);
    
    setColumnWidths(prev => ({
      ...prev,
      [column]: `${newWidth}px`
    }));
  };

  const handleMouseUp = () => {
    resizingRef.current = null;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  return (
    <div className={className}>
      <AddLineButton onClick={onAddLine}>
        <i className="fas fa-plus" style={{ marginRight: '4px' }} />
        Add a line
      </AddLineButton>
      
      <TableContainer>
        <Table>
          <TableHeader>
            <HeaderRow>
              {showDate && (
                <HeaderCell width={columnWidths.date} minWidth="100px">
                  Date
                  <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'date')} />
                </HeaderCell>
              )}
              <HeaderCell width={columnWidths.account} minWidth="150px">
                Account
                <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'account')} />
              </HeaderCell>
              <HeaderCell width={columnWidths.description} minWidth="150px">
                Label
                <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'description')} />
              </HeaderCell>
              <HeaderCell width={columnWidths.partner} minWidth="120px">
                Partner
                <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'partner')} />
              </HeaderCell>
              <HeaderCell width={columnWidths.debit} minWidth="100px">
                Debit
                <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'debit')} />
              </HeaderCell>
              <HeaderCell width={columnWidths.credit} minWidth="100px">
                Credit
                <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'credit')} />
              </HeaderCell>
              {showSalesTax && (
                <HeaderCell width={columnWidths.salesTax} minWidth="100px">
                  Sales Tax
                  <ResizeHandle onMouseDown={(e) => handleMouseDown(e, 'salesTax')} />
                </HeaderCell>
              )}
              <HeaderCell width={columnWidths.action} minWidth="60px">
                Action
              </HeaderCell>
            </HeaderRow>
          </TableHeader>
          
          <TableBody>
            {lines.map((line, index) => (
              <TableRow key={index}>
                {showDate && (
                  <TableCell width={columnWidths.date}>
                    <Input
                      type="date"
                      value={line.date || ''}
                      onChange={(e) => handleLineChange(index, 'date', e.target.value)}
                      variant="text"
                    />
                  </TableCell>
                )}

                <TableCell width={columnWidths.account}>
                  <SearchableSelect
                    value={line.account || ''}
                    onChange={handleAccountChange(index)}
                    options={[
                      { value: '', label: 'Select account...' },
                      ...(Array.isArray(accounts) && accounts.length > 0 ? accounts.map(account => ({
                        value: account.id,
                        label: `${account.code || account.id} - ${account.name || 'Unnamed Account'}`
                      })) : [])
                    ]}
                    placeholder="Select account..."
                    searchable={true}
                    fontSize="16px"
                  />
                </TableCell>

                <TableCell width={columnWidths.description}>
                  <Input
                    value={line.name || ''}
                    onChange={(e) => handleLineChange(index, 'name', e.target.value)}
                    placeholder="Enter description"
                    variant="text"
                  />
                </TableCell>

                <TableCell width={columnWidths.partner}>
                  <SearchableSelect
                    value={line.partner || ''}
                    onChange={handlePartnerChange(index)}
                    options={[
                      { value: '', label: 'Select partner...' },
                      ...(Array.isArray(partners) && partners.length > 0 ? partners.map(partner => ({
                        value: partner.id,
                        label: partner.name || `Partner ${partner.id}`
                      })) : [])
                    ]}
                    placeholder="Select partner..."
                    searchable={true}
                    fontSize="12px"
                  />
                </TableCell>

                <TableCell width={columnWidths.debit}>
                  <CurrencyInput
                    value={line.debit || ''}
                    onChange={(e) => handleLineChange(index, 'debit', e.target.value)}
                    placeholder=""
                    width="100%"
                  />
                </TableCell>

                <TableCell width={columnWidths.credit}>
                  <CurrencyInput
                    value={line.credit || ''}
                    onChange={(e) => handleLineChange(index, 'credit', e.target.value)}
                    placeholder=""
                    width="100%"
                  />
                </TableCell>

                {showSalesTax && (
                  <TableCell width={columnWidths.salesTax}>
                    <SearchableSelect
                      value={line.salesTax || ''}
                      onChange={(e) => handleLineChange(index, 'salesTax', e.target.value)}
                      options={[
                        { value: '', label: 'Select tax...' },
                        ...(Array.isArray(taxes) && taxes.length > 0 ? taxes.map(tax => ({
                          value: tax.id,
                          label: `${tax.name} (${tax.amount}%)`
                        })) : [])
                      ]}
                      placeholder="Select tax..."
                      searchable={true}
                      fontSize="12px"
                    />
                  </TableCell>
                )}

                <ActionCell width={columnWidths.action}>
                  {lines.length > 2 && (
                    <RemoveButton
                      onClick={() => onRemoveLine(index)}
                      title="Remove line"
                    >
                      <i className="fas fa-trash" />
                    </RemoveButton>
                  )}
                </ActionCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

JournalEntryTable.propTypes = {
  lines: PropTypes.array.isRequired,
  onLinesChange: PropTypes.func.isRequired,
  accounts: PropTypes.array,
  partners: PropTypes.array,
  taxes: PropTypes.array,
  onAddLine: PropTypes.func.isRequired,
  onRemoveLine: PropTypes.func.isRequired,
  showSalesTax: PropTypes.bool,
  showDate: PropTypes.bool,
  journalType: PropTypes.string,
  className: PropTypes.string,
};

export default JournalEntryTable;
