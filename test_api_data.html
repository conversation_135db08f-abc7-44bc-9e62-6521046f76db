<!DOCTYPE html>
<html>
<head>
    <title>Test API Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        pre { white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>API Data Test</h1>
    
    <button onclick="testJournals()">Test Journals API</button>
    <button onclick="testAccounts()">Test Accounts API</button>
    <button onclick="testPartners()">Test Partners API</button>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        const token = localStorage.getItem('access_token');
        const refreshToken = localStorage.getItem('refresh_token');
        const user = localStorage.getItem('user');

        // Show token info
        document.body.insertAdjacentHTML('afterbegin', `
            <div class="result">
                <h3>🔍 Token Debug Info</h3>
                <p><strong>Access Token:</strong> ${token ? token.substring(0, 50) + '...' : '❌ Not found'}</p>
                <p><strong>Refresh Token:</strong> ${refreshToken ? refreshToken.substring(0, 50) + '...' : '❌ Not found'}</p>
                <p><strong>User:</strong> ${user || '❌ Not found'}</p>
                <button onclick="testTokenValidity()">Test Token Validity</button>
                <button onclick="refreshAccessToken()">Refresh Token</button>
            </div>
        `);

        if (!token) {
            document.getElementById('result').innerHTML = '❌ No access token found. Please login first.';
            document.getElementById('result').style.display = 'block';
            document.getElementById('result').className = 'result error';
        }
        
        async function makeAPICall(endpoint) {
            const response = await fetch(`${API_BASE}${endpoint}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        }
        
        async function testJournals() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing journals API...';
            resultDiv.className = 'result';
            
            try {
                const data = await makeAPICall('/accounting/journals/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>✅ Journals API Success</h3>
                    <p><strong>Count:</strong> ${data.results ? data.results.length : (Array.isArray(data) ? data.length : 'Unknown')}</p>
                    <p><strong>Structure:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Journals API Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        async function testAccounts() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing accounts API...';
            resultDiv.className = 'result';
            
            try {
                const data = await makeAPICall('/accounting/accounts/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>✅ Accounts API Success</h3>
                    <p><strong>Count:</strong> ${data.results ? data.results.length : (Array.isArray(data) ? data.length : 'Unknown')}</p>
                    <p><strong>Structure:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Accounts API Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        async function testPartners() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing partners API...';
            resultDiv.className = 'result';

            try {
                const data = await makeAPICall('/partners/');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>✅ Partners API Success</h3>
                    <p><strong>Count:</strong> ${data.results ? data.results.length : (Array.isArray(data) ? data.length : 'Unknown')}</p>
                    <p><strong>Structure:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Partners API Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function testTokenValidity() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Testing token validity...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${API_BASE}/auth/token/verify/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token: token })
                });

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Token is Valid</h3>
                        <p>Your access token is working correctly.</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Token is Invalid</h3>
                        <p>Status: ${response.status}</p>
                        <p>Your token has expired or is invalid.</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Token Test Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function refreshAccessToken() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Refreshing access token...';
            resultDiv.className = 'result';

            if (!refreshToken) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ No Refresh Token</h3>
                    <p>Cannot refresh token. Please login again.</p>
                `;
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/token/refresh/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ refresh: refreshToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('access_token', data.access);

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Token Refreshed</h3>
                        <p>New access token: ${data.access.substring(0, 50)}...</p>
                        <p>Please refresh this page and try the API tests again.</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Token Refresh Failed</h3>
                        <p>Status: ${response.status}</p>
                        <p>Please login again.</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Refresh Failed</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
