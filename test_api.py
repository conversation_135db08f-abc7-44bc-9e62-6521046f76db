#!/usr/bin/env python
import requests
import json

# Test API endpoints
base_url = "http://localhost:8000/api/v1"

def test_endpoint(endpoint):
    try:
        response = requests.get(f"{base_url}{endpoint}")
        print(f"\n=== {endpoint} ===")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'results' in data:
                print(f"Count: {len(data['results'])}")
                if data['results']:
                    print("Sample record:", json.dumps(data['results'][0], indent=2))
            elif isinstance(data, list):
                print(f"Count: {len(data)}")
                if data:
                    print("Sample record:", json.dumps(data[0], indent=2))
            else:
                print("Data:", json.dumps(data, indent=2))
        else:
            print("Error:", response.text)
    except Exception as e:
        print(f"Error testing {endpoint}: {e}")

# Test all relevant endpoints
endpoints = [
    "/accounting/journals/",
    "/accounting/accounts/",
    "/partners/",
    "/companies/",
    "/currencies/"
]

for endpoint in endpoints:
    test_endpoint(endpoint)
