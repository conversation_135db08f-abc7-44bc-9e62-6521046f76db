#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.join(os.getcwd(), 'django_erp'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
django.setup()

from django.contrib.auth.models import User
from core.models import Company, Partner, Currency, Country
from accounting.models import AccountGroup, AccountAccount, AccountJournal

def create_basic_data():
    print("Creating basic test data...")
    
    # Get or create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin')
        admin_user.save()
    print(f"Admin user: {'created' if created else 'exists'}")

    # Create Currency
    usd, created = Currency.objects.get_or_create(
        name='USD',
        defaults={
            'symbol': '$',
            'decimal_places': 2,
            'full_name': 'US Dollar',
            'position': 'before',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Currency USD: {'created' if created else 'exists'}")

    # Create Country
    usa, created = Country.objects.get_or_create(
        code='US',
        defaults={
            'name': 'United States',
            'phone_code': 1,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Country US: {'created' if created else 'exists'}")

    # Create Company
    company, created = Company.objects.get_or_create(
        code='DEMO',
        defaults={
            'name': 'Demo ERP Company',
            'currency': usd,
            'email': '<EMAIL>',
            'phone': '******-0123',
            'website': 'https://demo-erp.com',
            'vat': 'US123456789',
            'street': '123 Business Ave',
            'city': 'New York',
            'zip': '10001',
            'country': usa,
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Company: {'created' if created else 'exists'}")

    # Create Account Groups
    asset_group, created = AccountGroup.objects.get_or_create(
        name='Assets',
        company=company,
        defaults={
            'code_prefix_start': '1000',
            'code_prefix_end': '1999',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Asset Group: {'created' if created else 'exists'}")

    liability_group, created = AccountGroup.objects.get_or_create(
        name='Liabilities',
        company=company,
        defaults={
            'code_prefix_start': '2000',
            'code_prefix_end': '2999',
            'create_uid': admin_user,
            'write_uid': admin_user
        }
    )
    print(f"Liability Group: {'created' if created else 'exists'}")

    # Create basic accounts
    accounts_data = [
        ('1000', 'Cash', 'asset_current', asset_group),
        ('1100', 'Bank', 'asset_current', asset_group),
        ('2000', 'Accounts Payable', 'liability_current', liability_group),
        ('4000', 'Sales Revenue', 'income', None),
        ('5000', 'Cost of Goods Sold', 'expense', None),
    ]

    for code, name, account_type, group in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'account_type': account_type,
                'group': group,
                'reconcile': False,
                'currency': usd,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"Account {code}: {'created' if created else 'exists'}")

    # Create Journals
    journals_data = [
        ('GJ', 'General Journal', 'general', None),
        ('BNK', 'Bank Journal', 'bank', AccountAccount.objects.get(code='1100', company=company)),
        ('CSH', 'Cash Journal', 'cash', AccountAccount.objects.get(code='1000', company=company)),
    ]

    for code, name, journal_type, default_account in journals_data:
        journal, created = AccountJournal.objects.get_or_create(
            code=code,
            company=company,
            defaults={
                'name': name,
                'type': journal_type,
                'default_account': default_account,
                'currency': usd,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"Journal {code}: {'created' if created else 'exists'}")

    # Create Partners
    partners_data = [
        ('ABC Corp', True, 1, 0, '<EMAIL>', '******-1001'),
        ('XYZ Ltd', True, 1, 0, '<EMAIL>', '******-1002'),
        ('Office Supplies Co', True, 0, 1, '<EMAIL>', '******-2001'),
        ('Equipment Rental LLC', True, 0, 1, '<EMAIL>', '******-2002'),
    ]

    for name, is_company, customer_rank, supplier_rank, email, phone in partners_data:
        partner, created = Partner.objects.get_or_create(
            name=name,
            defaults={
                'is_company': is_company,
                'customer_rank': customer_rank,
                'supplier_rank': supplier_rank,
                'email': email,
                'phone': phone,
                'street': f'{name} Street',
                'city': 'New York',
                'zip': '10001',
                'country': usa,
                'create_uid': admin_user,
                'write_uid': admin_user
            }
        )
        print(f"Partner {name}: {'created' if created else 'exists'}")

    print("\n✅ Basic test data created successfully!")
    print("🌐 Access admin at: http://localhost:8000/admin/")
    print("   Username: admin | Password: admin")

if __name__ == "__main__":
    create_basic_data()
