import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';
import { theme } from '../theme';
import LoadingSpinner from '../LoadingSpinner/LoadingSpinner';

const TableContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => !['maxHeight', 'size'].includes(prop),
})`
  width: 100%;
  overflow-x: auto;
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.lg};
  background-color: ${theme.colors.white};
  
  ${props => props.maxHeight && css`
    max-height: ${props.maxHeight};
    overflow-y: auto;
  `}
`;

const StyledTable = styled.table.withConfig({
  shouldForwardProp: (prop) => !['size'].includes(prop),
})`
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  font-family: ${theme.typography.fontFamily.odoo};

  ${props => props.size === 'sm' && css`
    font-size: 12px;
  `}

  ${props => props.size === 'lg' && css`
    font-size: 14px;
  `}
`;

const TableHeader = styled.thead`
  background-color: ${theme.colors.bgSecondary};
  border-bottom: 2px solid ${theme.colors.border};
`;

const TableHeaderRow = styled.tr`
  &:hover {
    background-color: ${theme.colors.bgSecondary};
  }
`;

const TableHeaderCell = styled.th.withConfig({
  shouldForwardProp: (prop) => !['sortable', 'sortDirection', 'clickable'].includes(prop),
})`
  padding: 8px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 12px;
  font-family: ${theme.typography.fontFamily.odoo};
  color: #495057;
  border-right: 1px solid ${theme.colors.borderLight};
  white-space: nowrap;
  
  &:last-child {
    border-right: none;
  }
  
  ${props => props.sortable && css`
    cursor: pointer;
    user-select: none;
    position: relative;
    
    &:hover {
      background-color: rgba(135, 90, 123, 0.1);
    }
    
    &::after {
      content: '';
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      opacity: 0.3;
      
      ${props.sortDirection === 'asc' && css`
        border-bottom: 6px solid ${theme.colors.textSecondary};
        opacity: 1;
      `}
      
      ${props.sortDirection === 'desc' && css`
        border-top: 6px solid ${theme.colors.textSecondary};
        opacity: 1;
      `}
      
      ${!props.sortDirection && css`
        border-bottom: 6px solid ${theme.colors.textMuted};
      `}
    }
  `}
  
  ${props => props.align === 'center' && css`
    text-align: center;
  `}
  
  ${props => props.align === 'right' && css`
    text-align: right;
  `}
  
  ${props => props.width && css`
    width: ${props.width};
    min-width: ${props.width};
  `}
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr.withConfig({
  shouldForwardProp: (prop) => !['clickable', 'selected'].includes(prop),
})`
  border-bottom: 1px solid ${theme.colors.borderLight};
  transition: all ${theme.transitions.fast};
  
  &:hover {
    background-color: rgba(135, 90, 123, 0.05);
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  ${props => props.clickable && css`
    cursor: pointer;
    
    &:hover {
      background-color: rgba(135, 90, 123, 0.1);
    }
  `}
  
  ${props => props.selected && css`
    background-color: rgba(135, 90, 123, 0.15);
    
    &:hover {
      background-color: rgba(135, 90, 123, 0.2);
    }
  `}
`;

const TableCell = styled.td.withConfig({
  shouldForwardProp: (prop) => !['align', 'clickable', 'balance', 'sticky'].includes(prop),
})`
  padding: 8px 12px;
  border-right: 1px solid ${theme.colors.borderLight};
  vertical-align: middle;
  font-size: 13px;
  font-family: ${theme.typography.fontFamily.odoo};
  color: #212529;
  
  &:last-child {
    border-right: none;
  }
  
  ${props => props.align === 'center' && css`
    text-align: center;
  `}
  
  ${props => props.align === 'right' && css`
    text-align: right;
  `}
  
  ${props => props.width && css`
    width: ${props.width};
    min-width: ${props.width};
  `}
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing['2xl']};
  color: ${theme.colors.textMuted};
  
  i {
    font-size: ${theme.typography.fontSize['3xl']};
    margin-bottom: ${theme.spacing.md};
    opacity: 0.5;
  }
  
  h3 {
    margin: 0 0 ${theme.spacing.sm};
    font-size: ${theme.typography.fontSize.lg};
    font-weight: ${theme.typography.fontWeight.medium};
  }
  
  p {
    margin: 0;
    font-size: ${theme.typography.fontSize.sm};
  }
`;

const LoadingOverlay = styled.div`
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Table = ({
  columns = [],
  data = [],
  loading = false,
  empty = null,
  sortable = false,
  onSort,
  sortBy,
  sortDirection,
  onRowClick,
  selectedRows = [],
  size = 'md',
  maxHeight,
  className = '',
  ...props
}) => {
  const [internalSort, setInternalSort] = useState({
    column: sortBy || null,
    direction: sortDirection || null,
  });

  // Handle sorting
  const handleSort = (column) => {
    if (!column.sortable && !sortable) return;

    let newDirection = 'asc';
    if (internalSort.column === column.key) {
      if (internalSort.direction === 'asc') {
        newDirection = 'desc';
      } else if (internalSort.direction === 'desc') {
        newDirection = null;
      }
    }

    const newSort = {
      column: newDirection ? column.key : null,
      direction: newDirection,
    };

    setInternalSort(newSort);

    if (onSort) {
      onSort(newSort.column, newSort.direction);
    }
  };

  // Sort data if no external sorting
  const sortedData = useMemo(() => {
    if (!internalSort.column || !internalSort.direction || onSort) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = a[internalSort.column];
      const bValue = b[internalSort.column];

      if (aValue === bValue) return 0;

      const comparison = aValue < bValue ? -1 : 1;
      return internalSort.direction === 'asc' ? comparison : -comparison;
    });
  }, [data, internalSort, onSort]);

  // Render cell content
  const renderCell = (item, column) => {
    // Handle undefined/null items
    if (!item) {
      return '-';
    }

    if (column.render) {
      return column.render(item[column.key], item);
    }
    return item[column.key];
  };

  // Handle row click
  const handleRowClick = (item, index) => {
    if (onRowClick) {
      onRowClick(item, index);
    }
  };

  // Check if row is selected
  const isRowSelected = (item, index) => {
    if (Array.isArray(selectedRows)) {
      return selectedRows.includes(index) || selectedRows.includes(item.id);
    }
    return false;
  };

  if (loading) {
    return (
      <TableContainer className={className}>
        <LoadingOverlay>
          <LoadingSpinner text="Loading data..." />
        </LoadingOverlay>
      </TableContainer>
    );
  }

  if (!data.length) {
    return (
      <TableContainer className={className}>
        {empty || (
          <EmptyState>
            <i className="fas fa-table" />
            <h3>No Data Available</h3>
            <p>There are no records to display at this time.</p>
          </EmptyState>
        )}
      </TableContainer>
    );
  }

  return (
    <TableContainer maxHeight={maxHeight} className={className} {...props}>
      <StyledTable size={size}>
        <TableHeader>
          <TableHeaderRow>
            {columns.map((column) => (
              <TableHeaderCell
                key={column.key}
                sortable={column.sortable || sortable}
                sortDirection={
                  internalSort.column === column.key ? internalSort.direction : null
                }
                align={column.align}
                width={column.width}
                onClick={() => handleSort(column)}
              >
                {column.title}
              </TableHeaderCell>
            ))}
          </TableHeaderRow>
        </TableHeader>
        <TableBody>
          {sortedData.filter(item => item != null).map((item, index) => (
            <TableRow
              key={item.id || index}
              clickable={!!onRowClick}
              selected={isRowSelected(item, index)}
              onClick={() => handleRowClick(item, index)}
            >
              {columns.map((column) => (
                <TableCell
                  key={column.key}
                  align={column.align}
                  width={column.width}
                >
                  {renderCell(item, column)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </StyledTable>
    </TableContainer>
  );
};

Table.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      sortable: PropTypes.bool,
      align: PropTypes.oneOf(['left', 'center', 'right']),
      width: PropTypes.string,
      render: PropTypes.func,
    })
  ),
  data: PropTypes.array,
  loading: PropTypes.bool,
  empty: PropTypes.node,
  sortable: PropTypes.bool,
  onSort: PropTypes.func,
  sortBy: PropTypes.string,
  sortDirection: PropTypes.oneOf(['asc', 'desc']),
  onRowClick: PropTypes.func,
  selectedRows: PropTypes.array,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  maxHeight: PropTypes.string,
  className: PropTypes.string,
};

export default Table;
