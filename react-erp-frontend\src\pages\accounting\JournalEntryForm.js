import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  PageContainer,
  PageHeader,
  PageTitle,
  Form,
  Section,
  Row,
  Column,
  Input,
  SearchableSelect,
  Button,
  Alert,
  LoadingSpinner,
  JournalEntryTable
} from '../../components/ui';
import { accountingAPI, coreAPI } from '../../services/api';

const JournalEntryForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { user, isAuthenticated } = useAuth();
  const isEdit = !!id;

  // Loading states
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Data states
  const [journals, setJournals] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [partners, setPartners] = useState([]);
  const [taxes, setTaxes] = useState([]);

  // Form data state
  const [formData, setFormData] = useState({
    ref: '',
    date: new Date().toISOString().split('T')[0],
    journal: '',
    partner: '',
    narration: '',
    line_ids: [
      { account: '', name: '', debit: 0, credit: 0, partner: '' },
      { account: '', name: '', debit: 0, credit: 0, partner: '' }
    ]
  });

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Starting to load data...');
      console.log('Auth status:', { isAuthenticated, user: user?.username });
      console.log('Auth token:', localStorage.getItem('token') ? 'Present' : 'Missing');

      if (!isAuthenticated) {
        setError('You are not authenticated. Please log in first.');
        setLoading(false);
        return;
      }

      // Load all required data
      const responses = await Promise.all([
        accountingAPI.getJournals().catch(err => {
          console.error('Failed to load journals:', err);
          return { data: { results: [] } };
        }),
        accountingAPI.getAccounts().catch(err => {
          console.error('Failed to load accounts:', err);
          return { data: { results: [] } };
        }),
        coreAPI.getPartners().catch(err => {
          console.error('Failed to load partners:', err);
          return { data: { results: [] } };
        }),
        accountingAPI.getTaxes().catch(err => {
          console.error('Failed to load taxes:', err);
          return { data: { results: [] } };
        })
      ]);

      console.log('Raw API responses:', {
        journals: responses[0],
        accounts: responses[1],
        partners: responses[2]
      });

      const journalsData = responses[0]?.results || responses[0]?.data?.results || responses[0]?.data || [];
      const accountsData = responses[1]?.results || responses[1]?.data?.results || responses[1]?.data || [];
      const partnersData = responses[2]?.results || responses[2]?.data?.results || responses[2]?.data || [];
      const taxesData = responses[3]?.results || responses[3]?.data?.results || responses[3]?.data || [];

      console.log('API Data loaded:', {
        journals: journalsData.length,
        accounts: accountsData.length,
        partners: partnersData.length,
        taxes: taxesData.length
      });

      setJournals(journalsData);
      setAccounts(accountsData);
      setPartners(partnersData);
      setTaxes(taxesData);

      console.log('Journals loaded:', journalsData.length, journalsData);
      console.log('Accounts loaded:', accountsData.length);
      console.log('Partners loaded:', partnersData.length);

      console.log('Final data:', {
        journals: journalsData.length,
        accounts: accountsData.length,
        partners: partnersData.length
      });

      if (journalsData.length > 0) {
        console.log('Sample journal:', journalsData[0]);
      }
      if (accountsData.length > 0) {
        console.log('Sample account:', accountsData[0]);
      }
      if (partnersData.length > 0) {
        console.log('Sample partner:', partnersData[0]);
      }

    } catch (err) {
      console.error('Error loading data:', err);
      setError(`Failed to load data: ${err.message}`);
      setJournals([]);
      setAccounts([]);
      setPartners([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle line items changes
  const handleLinesChange = (lines) => {
    setFormData(prev => ({
      ...prev,
      line_ids: lines
    }));
  };

  // Add new line
  const addLine = () => {
    const newLine = { account: '', name: '', debit: 0, credit: 0, partner: '' };
    setFormData(prev => ({
      ...prev,
      line_ids: [...prev.line_ids, newLine]
    }));
  };

  // Remove line
  const removeLine = (index) => {
    if (formData.line_ids.length > 2) {
      setFormData(prev => ({
        ...prev,
        line_ids: prev.line_ids.filter((_, i) => i !== index)
      }));
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    const totalDebit = formData.line_ids.reduce((sum, line) => sum + parseFloat(line.debit || 0), 0);
    const totalCredit = formData.line_ids.reduce((sum, line) => sum + parseFloat(line.credit || 0), 0);
    const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;

    return { totalDebit, totalCredit, isBalanced };
  };

  // Handle form submission
  const handleSave = async () => {
    setSubmitting(true);
    setError(null);

    try {
      // Validate form
      if (!formData.journal) {
        setError('Please select a journal');
        return;
      }

      if (!formData.date) {
        setError('Please enter a date');
        return;
      }

      const { isBalanced } = calculateTotals();
      if (!isBalanced) {
        setError('Journal entry must be balanced (total debits must equal total credits)');
        return;
      }

      // Validate line items
      const validLines = formData.line_ids.filter(line =>
        line.account && (parseFloat(line.debit || 0) > 0 || parseFloat(line.credit || 0) > 0)
      );

      if (validLines.length === 0) {
        setError('Please add at least one journal entry line with an account and amount');
        return;
      }

      console.log('Saving journal entry:', formData);

      // Get company and currency from selected journal if available
      const selectedJournal = journals.find(j => j.id === formData.journal);
      const companyId = selectedJournal?.company || null;
      const currencyId = selectedJournal?.currency || null;

      const payload = {
        ref: formData.ref,
        date: formData.date,
        journal: formData.journal, // Keep as UUID string, don't convert to int
        partner: formData.partner || null, // Keep as UUID string, don't convert to int
        narration: formData.narration,
        company: companyId,
        currency: currencyId,
        move_type: 'entry', // Explicitly set move type for journal entries
        line_ids: validLines.map(line => ({
          account: line.account, // Keep as UUID string, don't convert to int
          name: line.name || formData.narration || 'Journal Entry Line',
          date: formData.date,
          debit: parseFloat(line.debit || 0),
          credit: parseFloat(line.credit || 0),
          partner: line.partner || null // Keep as UUID string, don't convert to int
        }))
      };

      console.log('API Payload:', payload);
      console.log('Valid lines:', validLines);

      let response;
      if (isEdit) {
        response = await accountingAPI.updateJournalEntry(id, payload);
      } else {
        response = await accountingAPI.createJournalEntry(payload);
      }

      console.log('Save response:', response);
      navigate('/accounting/journal-entries');
    } catch (err) {
      console.error('Error saving journal entry:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);
      console.error('Full error object:', err);

      let errorMessage = 'Failed to save journal entry';
      if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage += `: ${err.response.data}`;
        } else if (err.response.data.detail) {
          errorMessage += `: ${err.response.data.detail}`;
        } else if (err.response.data.error) {
          errorMessage += `: ${err.response.data.error}`;
        } else {
          errorMessage += `: ${JSON.stringify(err.response.data)}`;
        }
      } else {
        errorMessage += `: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/accounting/journal-entries');
  };

  if (loading) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
          <LoadingSpinner />
        </div>
      </PageContainer>
    );
  }

  const { totalDebit, totalCredit, isBalanced } = calculateTotals();

  return (
    <PageContainer>
      <PageHeader>
        <div className="header-content">
          <PageTitle>{isEdit ? 'Edit' : 'Create'} Journal Entry</PageTitle>
        </div>
        <div className="header-actions">
          <Button
            variant="outline"
            color="secondary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={submitting || !isBalanced}
          >
            {submitting ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </PageHeader>

      {error && (
        <Alert type="error" style={{ marginBottom: '20px' }}>
          {error}
        </Alert>
      )}

      <Form>
        {/* Entry Details */}
        <Section title="Entry Details" border>
          <Row columns={3}>
            <Column>
              <Input
                label="Reference"
                name="ref"
                value={formData.ref}
                onChange={handleInputChange}
                placeholder="Enter reference"
              />
            </Column>
            <Column>
              <Input
                label="Date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleInputChange}
                required
              />
            </Column>
            <Column>
              <SearchableSelect
                label={`Journal (${journals.length} available)`}
                name="journal"
                value={formData.journal}
                onChange={handleInputChange}
                options={[
                  { value: '', label: 'Select journal...' },
                  ...journals.map(journal => ({
                    value: journal.id,
                    label: `${journal.code} - ${journal.name}`
                  }))
                ]}
                required
                searchable
                fontSize="12px"
              />
            </Column>
          </Row>

          <Row columns={2}>
            <Column>
              <SearchableSelect
                label="Partner"
                name="partner"
                value={formData.partner}
                onChange={handleInputChange}
                options={[
                  { value: '', label: 'Select partner...' },
                  ...partners.map(partner => ({
                    value: partner.id,
                    label: partner.name
                  }))
                ]}
                searchable
                fontSize="12px"
              />
            </Column>
            <Column>
              <Input
                label="Narration"
                name="narration"
                value={formData.narration}
                onChange={handleInputChange}
                placeholder="Enter description"
              />
            </Column>
          </Row>
        </Section>

        {/* Journal Items */}
        <Section title="Journal Items" border>
          <JournalEntryTable
            lines={formData.line_ids}
            onLinesChange={handleLinesChange}
            accounts={accounts}
            partners={partners}
            taxes={taxes}
            onAddLine={addLine}
            onRemoveLine={removeLine}
            showSalesTax={true}
            showDate={false}
          />

          {/* Totals */}
          <div style={{
            marginTop: '16px',
            padding: '16px',
            backgroundColor: isBalanced ? '#d4edda' : '#f8d7da',
            border: `1px solid ${isBalanced ? '#c3e6cb' : '#f5c6cb'}`,
            borderRadius: '4px'
          }}>
            <Row columns={3}>
              <Column>
                <strong>Total Debit: {totalDebit.toFixed(2)}</strong>
              </Column>
              <Column>
                <strong>Total Credit: {totalCredit.toFixed(2)}</strong>
              </Column>
              <Column>
                <strong style={{ color: isBalanced ? '#155724' : '#721c24' }}>
                  {isBalanced ? '✓ Balanced' : '✗ Not Balanced'}
                </strong>
              </Column>
            </Row>
          </div>
        </Section>

        {/* Actions - Odoo Style */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '12px',
          marginTop: '24px',
          paddingTop: '16px',
          borderTop: '1px solid #e9ecef'
        }}>
          {/* Left side - Cancel */}
          <Button
            type="button"
            variant="outline"
            color="secondary"
            onClick={() => navigate('/accounting/journal-entries')}
            disabled={submitting}
          >
            Cancel
          </Button>

          {/* Right side - Save buttons */}
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type="button"
              color="primary"
              onClick={handleSave}
              disabled={submitting || !isBalanced}
              loading={submitting}
            >
              {submitting ? 'Saving...' : 'Save'}
            </Button>
            <Button
              type="button"
              color="primary"
              onClick={async () => {
                await handleSave();
                navigate('/accounting/journal-entries');
              }}
              disabled={submitting || !isBalanced}
            >
              Save & Close
            </Button>
            <Button
              type="button"
              color="primary"
              onClick={async () => {
                await handleSave();
                window.location.reload();
              }}
              disabled={submitting || !isBalanced}
            >
              Save & New
            </Button>
          </div>
        </div>
      </Form>
    </PageContainer>
  );
};

export default JournalEntryForm;
