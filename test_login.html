<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
    </style>
</head>
<body>
    <h1>Login Test</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label>Username:</label><br>
            <input type="text" id="username" value="admin" required>
        </div>
        <div class="form-group">
            <label>Password:</label><br>
            <input type="password" id="password" value="admin" required>
        </div>
        <div class="form-group">
            <button type="submit">Login</button>
        </div>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Logging in...';
            resultDiv.className = 'result';
            
            try {
                console.log('Attempting login with:', { username, password });
                
                const response = await fetch('http://localhost:8000/api/v1/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>User:</strong> ${data.user.username}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Token:</strong> ${data.access.substring(0, 50)}...</p>
                        <p><strong>Full Response:</strong></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Check if the Django server is running on port 8000</p>
                `;
            }
        });
    </script>
</body>
</html>
