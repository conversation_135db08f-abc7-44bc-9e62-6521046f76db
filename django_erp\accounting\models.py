from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
import re
from core.models import BaseModel, Company, Partner, Currency


class AccountType(BaseModel):
    """Account Type model - manages available account types dynamically"""

    INTERNAL_GROUP_CHOICES = [
        ('equity', 'Equity'),
        ('asset', 'Asset'),
        ('liability', 'Liability'),
        ('income', 'Income'),
        ('expense', 'Expense'),
        ('off_balance', 'Off Balance'),
    ]

    code = models.Char<PERSON>ield(max_length=50, unique=True, db_index=True, help_text="Unique code for the account type")
    name = models.CharField(max_length=100, help_text="Display name for the account type")
    internal_group = models.CharField(max_length=20, choices=INTERNAL_GROUP_CHOICES, db_index=True, help_text="Internal group classification")
    description = models.TextField(blank=True, help_text="Description of when to use this account type")
    requires_reconciliation = models.BooleanField(default=False, help_text="Whether accounts of this type must be reconcilable")
    is_system_type = models.BooleanField(default=False, db_index=True, help_text="System types cannot be deleted")
    active = models.Boolean<PERSON>ield(default=True, db_index=True, help_text="Whether this account type is available for selection")
    sequence = models.IntegerField(default=10, help_text="Display order")

    class Meta:
        ordering = ['internal_group', 'sequence', 'name']
        indexes = [
            models.Index(fields=['internal_group']),
            models.Index(fields=['active']),
            models.Index(fields=['is_system_type']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        super().clean()

        # Validate code format (no spaces, alphanumeric and underscore only)
        if not re.match(r'^[a-zA-Z0-9_]+$', self.code):
            raise ValidationError("Account type code can only contain alphanumeric characters and underscores.")

    def can_be_deleted(self):
        """Check if this account type can be deleted"""
        if self.is_system_type:
            return False, "System account types cannot be deleted."

        # Check if any accounts use this type
        accounts_using_type = AccountAccount.objects.filter(account_type=self.code)
        if accounts_using_type.exists():
            count = accounts_using_type.count()
            return False, f"Cannot delete account type that is used by {count} account(s)."

        return True, ""

    def can_be_modified(self):
        """Check what properties can be modified"""
        accounts_using_type = AccountAccount.objects.filter(account_type=self.code)
        has_accounts = accounts_using_type.exists()

        return {
            'can_change_code': not has_accounts,  # Cannot change code if accounts use it
            'can_change_internal_group': not self.is_system_type and not has_accounts,  # Restricted for system types and used types
            'can_change_name': True,  # Name can always be changed
            'can_change_description': True,  # Description can always be changed
            'can_change_reconciliation': not has_accounts,  # Cannot change if accounts exist
            'can_deactivate': not has_accounts,  # Cannot deactivate if accounts use it
            'accounts_count': accounts_using_type.count() if has_accounts else 0
        }

    def get_usage_info(self):
        """Get information about where this account type is used"""
        accounts_using_type = AccountAccount.objects.filter(account_type=self.code)

        info = {
            'accounts_count': accounts_using_type.count(),
            'accounts': [],
            'can_delete': True,
            'delete_reason': ''
        }

        if accounts_using_type.exists():
            info['accounts'] = [
                {'id': str(acc.id), 'code': acc.code, 'name': acc.name}
                for acc in accounts_using_type[:5]  # Show first 5 accounts
            ]

        can_delete, reason = self.can_be_deleted()
        info['can_delete'] = can_delete
        info['delete_reason'] = reason

        return info


class AccountGroup(BaseModel):
    """Account Group model - equivalent to account.group in Odoo"""
    name = models.CharField(max_length=255)
    code_prefix_start = models.CharField(max_length=20, blank=True)
    code_prefix_end = models.CharField(max_length=20, blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    class Meta:
        # Note: Length constraint will be handled in clean() method for cross-database compatibility
        pass

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        # Validate that prefix start and end have the same length
        if self.code_prefix_start and self.code_prefix_end:
            if len(self.code_prefix_start) != len(self.code_prefix_end):
                raise ValidationError("The length of the starting and ending code prefix must be the same")

class AccountAccount(BaseModel):
    """Account model - equivalent to account.account in Odoo"""

    # Account type choices are now dynamic - loaded from AccountType model
    # No hardcoded choices needed since validation is done in clean() method

    INTERNAL_GROUP_CHOICES = [
        ('equity', 'Equity'),
        ('asset', 'Asset'),
        ('liability', 'Liability'),
        ('income', 'Income'),
        ('expense', 'Expense'),
        ('off_balance', 'Off Balance'),
    ]

    name = models.CharField(max_length=255, db_index=True)
    code = models.CharField(max_length=64, db_index=True)
    account_type = models.CharField(max_length=50, help_text="Account type code - validated against AccountType model")
    internal_group = models.CharField(
        max_length=20,
        choices=INTERNAL_GROUP_CHOICES,
        editable=False,
        help_text="Computed from account type - matches Odoo's internal_group field"
    )
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Reconciliation and control
    reconcile = models.BooleanField(default=False, help_text="Allow reconciliation of journal items")
    deprecated = models.BooleanField(default=False)

    # Grouping
    group = models.ForeignKey(AccountGroup, on_delete=models.SET_NULL, null=True, blank=True)

    # Tax configuration
    taxes = models.ManyToManyField('AccountTax', blank=True, help_text="Default taxes for this account")

    # Notes
    note = models.TextField(blank=True, null=True, help_text="Internal notes")

    class Meta:
        unique_together = [['code', 'company']]
        indexes = [
            models.Index(fields=['code', 'company']),
            models.Index(fields=['account_type']),
            models.Index(fields=['internal_group']),
        ]

    def __str__(self):
        return f"{self.code} {self.name}"

    def save(self, *args, **kwargs):
        """Override save to auto-compute internal_group like Odoo"""
        if self.account_type:
            try:
                # Get internal_group from AccountType model instead of parsing code
                account_type_obj = AccountType.objects.get(code=self.account_type, active=True)
                self.internal_group = account_type_obj.internal_group
            except AccountType.DoesNotExist:
                # Fallback to parsing code if AccountType not found
                if self.account_type == 'off_balance':
                    self.internal_group = 'off_balance'
                else:
                    # Extract the first part before underscore (asset_receivable -> asset)
                    self.internal_group = self.account_type.split('_')[0]
        super().save(*args, **kwargs)

    def clean(self):
        super().clean()

        # Validate account code format (alphanumeric and dots only)
        if not re.match(r'^[a-zA-Z0-9.]+$', self.code):
            raise ValidationError("Account code can only contain alphanumeric characters and dots.")

        # Validate account_type exists in AccountType table
        if self.account_type:
            try:
                account_type_obj = AccountType.objects.get(code=self.account_type, active=True)

                # Receivable/Payable accounts must be reconcilable
                if self.account_type in ('asset_receivable', 'liability_payable') and not self.reconcile:
                    raise ValidationError(f"Receivable/Payable account {self.code} must be reconcilable.")

                # Check if account type requires reconciliation
                if account_type_obj.requires_reconciliation and not self.reconcile:
                    raise ValidationError(f"Account type '{account_type_obj.name}' requires reconciliation to be enabled.")

                # Off-balance accounts cannot be reconcilable
                if self.account_type == 'off_balance':
                    if self.reconcile:
                        raise ValidationError("Off-Balance account cannot be reconcilable.")

            except AccountType.DoesNotExist:
                raise ValidationError(f"Invalid account type '{self.account_type}'. Please select a valid account type.")

        # Validate account type changes for existing accounts
        if self.pk:
            try:
                old_account = AccountAccount.objects.get(pk=self.pk)
                if old_account.account_type != self.account_type:
                    self._validate_account_type_change(old_account.account_type)
            except AccountAccount.DoesNotExist:
                pass

    def _validate_account_type_change(self, old_type):
        """Validate account type changes following Odoo's business rules"""
        # Import here to avoid circular imports
        from django.apps import apps
        AccountMoveLine = apps.get_model('accounting', 'AccountMoveLine')

        # Check if account has journal entries (move lines)
        has_entries = AccountMoveLine.objects.filter(account=self).exists()

        # 1. Check equity_unaffected uniqueness (Current Year Earnings)
        if self.account_type == 'equity_unaffected':
            existing = AccountAccount.objects.filter(
                account_type='equity_unaffected',
                company=self.company
            ).exclude(pk=self.pk)
            if existing.exists():
                raise ValidationError(
                    "Only one Current Year Earnings account is allowed per company. "
                    f"Account '{existing.first().code} - {existing.first().name}' already has this type."
                )

        # 2. Validate receivable/payable constraints
        if self.account_type in ('asset_receivable', 'liability_payable'):
            if not self.reconcile:
                raise ValidationError(
                    f"Cannot change account type to {self.get_account_type_display()} "
                    "without enabling reconciliation. Please enable 'Allow Reconciliation' first."
                )

        # 3. Off-balance account restrictions
        if old_type == 'off_balance' and self.account_type != 'off_balance':
            if has_entries:
                raise ValidationError(
                    "Cannot change account type from Off-Balance to regular account "
                    "when the account has existing journal entries."
                )

        # 4. Prevent changes that would break financial reporting
        if old_type == 'equity_unaffected' and has_entries:
            if self.account_type not in ['equity', 'equity_unaffected']:
                raise ValidationError(
                    "Current Year Earnings account can only be changed to regular Equity type "
                    "when it has existing entries."
                )

    def can_change_account_type(self):
        """Check if account type can be changed and return restrictions"""
        from django.apps import apps
        AccountMoveLine = apps.get_model('accounting', 'AccountMoveLine')

        restrictions = {
            'can_change': True,
            'warnings': [],
            'errors': [],
            'has_entries': False
        }

        # Check if account has journal entries
        has_entries = AccountMoveLine.objects.filter(account=self).exists()
        restrictions['has_entries'] = has_entries

        if has_entries:
            restrictions['warnings'].append(
                "This account has existing journal entries. Changing the account type "
                "may affect financial reports and reconciliation."
            )

        # Check specific restrictions
        if self.account_type == 'equity_unaffected' and has_entries:
            restrictions['warnings'].append(
                "This is a Current Year Earnings account with entries. "
                "It can only be changed to regular Equity type."
            )

        return restrictions

class AccountJournal(BaseModel):
    """Journal model - equivalent to account.journal in Odoo"""

    JOURNAL_TYPE_CHOICES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=10)
    type = models.CharField(max_length=20, choices=JOURNAL_TYPE_CHOICES)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)

    # Default accounts
    default_account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                         related_name='journal_default_account')
    suspense_account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                          related_name='journal_suspense_account', null=True, blank=True)

    # Control
    account_control = models.ManyToManyField(AccountAccount, blank=True,
                                               help_text="Allowed accounts for this journal")

    # Sequence and numbering
    sequence = models.IntegerField(default=10, help_text="Used to order journals")

    # Security
    restrict_mode_hash_table = models.BooleanField(default=True,
                                                 help_text="Lock posted entries with hash")

    class Meta:
        unique_together = [['code', 'company']]

    def __str__(self):
        return f"{self.code} - {self.name}"

class AccountTax(BaseModel):
    """Tax model - equivalent to account.tax in Odoo"""

    TAX_TYPE_CHOICES = [
        ('sale', 'Sales Tax'),
        ('purchase', 'Purchase Tax'),
        ('withholding', 'Withholding Tax'),
        ('none', 'None'),
    ]

    TAX_SCOPE_CHOICES = [
        ('service', 'Services'),
        ('consu', 'Goods'),
    ]

    AMOUNT_TYPE_CHOICES = [
        ('group', 'Group of Taxes'),
        ('fixed', 'Fixed'),
        ('percent', 'Percentage of Price'),
        ('division', 'Percentage of Price Tax Included'),
    ]

    name = models.CharField(max_length=255)
    type_tax_use = models.CharField(max_length=20, choices=TAX_TYPE_CHOICES, default='sale')
    tax_scope = models.CharField(max_length=20, choices=TAX_SCOPE_CHOICES, default='consu')
    amount_type = models.CharField(max_length=20, choices=AMOUNT_TYPE_CHOICES, default='percent')
    amount = models.DecimalField(max_digits=16, decimal_places=4, default=0.0)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Additional tax configuration
    description = models.CharField(max_length=255, blank=True, help_text="Tax description for invoices")
    active = models.BooleanField(default=True, help_text="Set to false to hide tax without deleting")
    price_include = models.BooleanField(default=False, help_text="Tax included in price")

    # Withholding tax support
    is_withholding = models.BooleanField(default=False, help_text="Is this a withholding tax?")
    withholding_type = models.CharField(max_length=50, blank=True,
                                       help_text="Type of withholding (e.g., 'income_tax', 'vat_withholding')")

    # Tax accounts
    tax_payable_account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                          related_name='payable_taxes', null=True, blank=True,
                                          help_text="Account for tax payable")
    tax_receivable_account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                             related_name='receivable_taxes', null=True, blank=True,
                                             help_text="Account for tax receivable")

    # Accounts for tax computation
    invoice_repartition_lines = models.ManyToManyField('AccountTaxRepartitionLine',
                                                        related_name='invoice_taxs', blank=True)
    refund_repartition_lines = models.ManyToManyField('AccountTaxRepartitionLine',
                                                       related_name='refund_taxs', blank=True)

    # Tax group and sequence
    sequence = models.IntegerField(default=1)

    class Meta:
        unique_together = [['name', 'type_tax_use', 'tax_scope', 'company']]

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        # Tax names must be unique per company for the same type and scope
        existing = AccountTax.objects.filter(
            name=self.name,
            type_tax_use=self.type_tax_use,
            tax_scope=self.tax_scope,
            company=self.company
        ).exclude(pk=self.pk)

        if existing.exists():
            raise ValidationError(f"Tax name '{self.name}' must be unique per company for the same type and scope.")

class AccountTaxRepartitionLine(BaseModel):
    """Tax Repartition Line model - equivalent to account.tax.repartition.line in Odoo"""

    REPARTITION_TYPE_CHOICES = [
        ('base', 'Base'),
        ('tax', 'Tax'),
    ]

    factor_percent = models.DecimalField(max_digits=16, decimal_places=4, default=100.0)
    repartition_type = models.CharField(max_length=10, choices=REPARTITION_TYPE_CHOICES, default='tax')
    account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT, null=True, blank=True)
    sequence = models.IntegerField(default=1)

    def __str__(self):
        return f"{self.repartition_type} - {self.factor_percent}%"

class AccountMove(BaseModel):
    """Journal Entry model - equivalent to account.move in Odoo"""

    MOVE_TYPE_CHOICES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    STATE_CHOICES = [
        ('draft', 'Unposted'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    PAYMENT_STATE_CHOICES = [
        ('not_paid', 'Not Paid'),
        ('in_payment', 'In Payment'),
        ('paid', 'Paid'),
        ('partial', 'Partially Paid'),
        ('reversed', 'Reversed'),
        ('invoicing_legacy', 'Invoicing App Legacy'),
    ]

    # Basic fields
    name = models.CharField(max_length=255, blank=True, help_text="Journal Entry Number")
    ref = models.CharField(max_length=255, blank=True, help_text="Reference")
    date = models.DateField(default=timezone.now)

    # Type and state
    move_type = models.CharField(max_length=20, choices=MOVE_TYPE_CHOICES, default='entry')
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')

    # Journal and company
    journal = models.ForeignKey(AccountJournal, on_delete=models.PROTECT)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Partner information (for invoices)
    partner = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    # Invoice specific fields
    invoice_date = models.DateField(null=True, blank=True)
    invoice_date_due = models.DateField(null=True, blank=True)
    payment_reference = models.CharField(max_length=255, blank=True)
    payment_state = models.CharField(max_length=20, choices=PAYMENT_STATE_CHOICES, default='not_paid')

    # Amounts (computed from lines)
    amount_untaxed = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_tax = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_total = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    amount_residual = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Control fields
    posted_before = models.BooleanField(default=False)
    to_check = models.BooleanField(default=False)

    # Hash and security
    inalterable_hash = models.CharField(max_length=255, blank=True)
    secure_sequence_number = models.IntegerField(null=True, blank=True)

    # Advanced Invoicing Fields
    invoice_origin = models.CharField(max_length=255, blank=True, help_text="Source Document")
    invoice_source_email = models.EmailField(blank=True, help_text="Source Email")
    invoice_partner_display_name = models.CharField(max_length=255, blank=True, help_text="Partner Display Name")

    # Shipping information
    partner_shipping = models.ForeignKey(Partner, on_delete=models.SET_NULL, null=True, blank=True,
                                          related_name='shipping_invoices', help_text="Delivery Address")

    # Payment terms and fiscal position
    invoice_payment_term = models.ForeignKey('AccountPaymentTerm', on_delete=models.SET_NULL,
                                               null=True, blank=True, help_text="Payment Terms")
    fiscal_position = models.ForeignKey('AccountFiscalPosition', on_delete=models.SET_NULL,
                                         null=True, blank=True, help_text="Fiscal Position")

    # Sales team information
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Salesperson")
    team = models.ForeignKey('crm.CrmTeam', on_delete=models.SET_NULL, null=True, blank=True,
                               help_text="Sales Team")

    # Approval workflow
    APPROVAL_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    approval_status = models.CharField(max_length=20, choices=APPROVAL_STATUS_CHOICES,
                                     default='draft', help_text="Approval Status")
    approved_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='approved_invoices', help_text="Approved By")
    approved_date = models.DateTimeField(null=True, blank=True, help_text="Approval Date")

    # Communication tracking
    invoice_sent = models.BooleanField(default=False, help_text="Invoice Sent")
    invoice_sent_date = models.DateTimeField(null=True, blank=True, help_text="Invoice Sent Date")

    # Recurring invoice fields
    is_recurring = models.BooleanField(default=False, help_text="Is Recurring Invoice")
    recurring_source = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                           help_text="Source Recurring Invoice")

    # Online payment
    payment_transactions = models.ManyToManyField('PaymentTransaction', blank=True,
                                                   help_text="Payment Transactions")

    # Auto-posting
    auto_post = models.BooleanField(default=False, help_text="Auto Post")
    auto_post_until = models.DateField(null=True, blank=True, help_text="Auto Post Until")

    class Meta:
        indexes = [
            models.Index(fields=['date', 'name']),
            models.Index(fields=['state']),
            models.Index(fields=['move_type']),
            models.Index(fields=['partner']),
            models.Index(fields=['journal']),
        ]

    def __str__(self):
        return self.name or f"Draft Move {self.id}"

    def save(self, *args, **kwargs):
        """Override save to generate name if not provided"""
        if not self.name and self.journal:
            # Generate a simple sequence number for the journal entry
            # In a real implementation, you'd want to use a proper sequence
            from django.db.models import Max
            last_move = AccountMove.objects.filter(
                journal=self.journal,
                name__isnull=False
            ).exclude(name='').aggregate(Max('id'))

            next_number = (last_move['id__max'] or 0) + 1
            self.name = f"{self.journal.code}/{self.date.year}/{next_number:04d}"

        super().save(*args, **kwargs)

    def clean(self):
        super().clean()

        # Cannot create move in posted state
        if self.state == 'posted' and not self.pk:
            raise ValidationError("Cannot create a move already in posted state. Create draft and post after.")

        # Journal type validation
        valid_types = self._get_valid_journal_types()
        if self.journal and self.journal.type not in valid_types:
            raise ValidationError(f"Journal type {self.journal.type} is not valid for move type {self.move_type}")

    def _get_valid_journal_types(self):
        """Get valid journal types for this move type"""
        if self.move_type in ('out_invoice', 'out_refund', 'out_receipt'):
            return ['sale']
        elif self.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
            return ['purchase']
        else:
            return ['general', 'bank', 'cash']

    def is_invoice(self, include_receipts=False):
        """Check if this move is an invoice"""
        invoice_types = ['out_invoice', 'in_invoice', 'out_refund', 'in_refund']
        if include_receipts:
            invoice_types.extend(['out_receipt', 'in_receipt'])
        return self.move_type in invoice_types

    def is_sale_document(self, include_receipts=False):
        """Check if this is a sales document"""
        sale_types = ['out_invoice', 'out_refund']
        if include_receipts:
            sale_types.append('out_receipt')
        return self.move_type in sale_types

    def is_purchase_document(self, include_receipts=False):
        """Check if this is a purchase document"""
        purchase_types = ['in_invoice', 'in_refund']
        if include_receipts:
            purchase_types.append('in_receipt')
        return self.move_type in purchase_types

    def action_post(self):
        """Post the journal entry with comprehensive validation and processing"""
        if self.state != 'draft':
            raise ValidationError("Only draft moves can be posted.")

        # Pre-posting validations
        self._validate_before_posting()

        # Validate that move is balanced
        self._validate_balanced_for_posting()

        # Process tax lines and calculations
        self._process_tax_lines()

        # Process currency conversion if needed
        self._process_currency_conversion()

        # Generate sequence number if needed
        if not self.name:
            self.name = self._get_sequence_number()

        # Update state and posted_before flag
        self.state = 'posted'
        self.posted_before = True
        self.save()

        # Post-posting processing
        self._update_partner_balances()
        self._update_account_balances()
        self._create_analytic_entries()

        # Update payment state for invoices
        if self.is_invoice():
            self._compute_payment_state()

        # Trigger related workflows
        self._trigger_workflows()

    def _validate_before_posting(self):
        """Comprehensive validation before posting"""
        # Check if journal allows posting
        if not self.journal.active:
            raise ValidationError("Cannot post to inactive journal")

        # Check if period is locked
        if self._is_in_locked_period():
            raise ValidationError("Cannot post in locked period")

        # Check if all lines have accounts
        for line in self.line_ids.all():
            if not line.display_type and not line.account:
                raise ValidationError(f"Line '{line.name}' must have an account")

        # Check if company matches
        for line in self.line_ids.all():
            if line.account and line.account.company != self.company:
                raise ValidationError("All accounts must belong to the same company")

        # Check for duplicate lines (same account, partner, amount)
        self._check_duplicate_lines()

    def _check_duplicate_lines(self):
        """Check for potentially duplicate lines"""
        lines = self.line_ids.filter(display_type__isnull=True)
        seen_lines = set()

        for line in lines:
            line_key = (line.account.id, line.partner.id if line.partner else None,
                       line.debit, line.credit, line.name)
            if line_key in seen_lines:
                # Warning only, not blocking
                pass  # Could add warning system here
            seen_lines.add(line_key)

    def _process_tax_lines(self):
        """Process and validate tax lines"""
        # Calculate taxes for invoice lines
        if self.is_invoice():
            self._calculate_invoice_taxes()

    def _calculate_invoice_taxes(self):
        """Calculate taxes for invoice lines"""
        # This would calculate taxes based on tax configuration
        # For now, simplified implementation
        pass

    def _process_currency_conversion(self):
        """Handle multi-currency conversions"""
        if self.currency != self.company.currency:
            # Convert amounts to company currency
            self._convert_to_company_currency()

    def _convert_to_company_currency(self):
        """Convert line amounts to company currency"""
        # This would handle currency conversion using exchange rates
        # For now, simplified implementation
        pass

    def _update_account_balances(self):
        """Update account balances after posting"""
        # Update cached balances for performance
        for line in self.line_ids.all():
            if line.account:
                # This would update account balance caches
                pass

    def _create_analytic_entries(self):
        """Create analytic accounting entries"""
        # Create entries for cost centers, projects, departments
        for line in self.line_ids.all():
            if hasattr(line, 'analytic_account') and line.analytic_account:
                # This would create analytic entries
                pass

    def _trigger_workflows(self):
        """Trigger related workflows after posting"""
        # Trigger automatic reconciliation for bank statements
        if self.journal.type == 'bank':
            self._trigger_bank_reconciliation()

        # Trigger payment matching for customer/vendor invoices
        if self.is_invoice():
            self._trigger_payment_matching()

    def _trigger_bank_reconciliation(self):
        """Trigger automatic bank reconciliation"""
        # This would trigger bank statement matching
        pass

    def _trigger_payment_matching(self):
        """Trigger automatic payment matching"""
        # This would match payments with invoices
        pass

    def _update_partner_balances(self):
        """Update partner balances after posting"""
        # Update partner balance caches for receivable/payable accounts
        for line in self.line_ids.all():
            if line.partner and line.account.account_type in ['asset_receivable', 'liability_payable']:
                # This would update partner balance caches
                # For now, simplified implementation
                pass

    def _check_balanced(self):
        """Check if the move is balanced (debits = credits)"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())
        return abs(total_debit - total_credit) < 0.01  # Allow small rounding differences

    def _get_sequence_number(self):
        """Generate sequence number for the move"""
        # Simple sequence generation - in production, this would be more sophisticated
        last_move = AccountMove.objects.filter(
            journal=self.journal,
            date__year=self.date.year
        ).order_by('-name').first()

        if last_move and last_move.name:
            try:
                last_num = int(last_move.name.split('/')[-1])
                return f"{self.journal.code}/{self.date.year}/{last_num + 1:04d}"
            except (ValueError, IndexError):
                pass

        return f"{self.journal.code}/{self.date.year}/0001"

    def _compute_payment_state(self):
        """Compute payment state for invoices"""
        if not self.is_invoice():
            self.payment_state = 'not_paid'
            return

        if abs(self.amount_residual) < 0.01:
            self.payment_state = 'paid'
        elif abs(self.amount_residual - self.amount_total) < 0.01:
            self.payment_state = 'not_paid'
        else:
            self.payment_state = 'partial'

    def _compute_amounts(self):
        """Compute total amounts from move lines"""
        lines = self.line_ids.all()

        # For invoices, compute based on invoice lines (excluding tax lines)
        if self.is_invoice():
            invoice_lines = lines.filter(display_type__isnull=True, tax_line_id__isnull=True)
            tax_lines = lines.filter(tax_line_id__isnull=False)

            if self.is_sale_document():
                self.amount_untaxed = sum(line.credit for line in invoice_lines)
                self.amount_tax = sum(line.credit for line in tax_lines)
            else:
                self.amount_untaxed = sum(line.debit for line in invoice_lines)
                self.amount_tax = sum(line.debit for line in tax_lines)

            self.amount_total = self.amount_untaxed + self.amount_tax

            # Compute residual (amount still to be paid)
            if self.is_sale_document():
                receivable_lines = lines.filter(account_id__account_type='asset_receivable')
                self.amount_residual = sum(line.balance for line in receivable_lines)
            else:
                payable_lines = lines.filter(account_id__account_type='liability_payable')
                self.amount_residual = -sum(line.balance for line in payable_lines)
        else:
            # For journal entries, just show total debits/credits
            self.amount_total = sum(line.debit for line in lines)
            self.amount_untaxed = self.amount_total
            self.amount_tax = 0.0
            self.amount_residual = 0.0

    def button_draft(self):
        """Reset move to draft state"""
        if not self.posted_before:
            raise ValidationError("Only moves that have been posted before can be reset to draft.")

        # Check if move can be unposted
        if not self._can_be_unposted():
            raise ValidationError("This move cannot be reset to draft due to business constraints.")

        self.state = 'draft'
        self.save()

    def _can_be_unposted(self):
        """Check if move can be unposted"""
        # Check for reconciled lines
        if self.line_ids.filter(reconciled=True).exists():
            return False

        # Check for locked period
        if self._is_in_locked_period():
            return False

        return True

    def _is_in_locked_period(self):
        """Check if move is in a locked period"""
        # Simplified - in real implementation would check fiscal year locks
        return False

    def button_cancel(self):
        """Cancel the move"""
        if self.state == 'posted':
            raise ValidationError("Posted moves cannot be cancelled. Reset to draft first.")

        self.state = 'cancel'
        self.save()

    def delete(self):
        """Override delete to add business rules"""
        # Check if any move is posted
        if self.state == 'posted':
            raise ValidationError("Posted moves cannot be deleted.")

        # Check for reconciled lines
        if self.line_ids.filter(reconciled=True).exists():
            raise ValidationError("Moves with reconciled lines cannot be deleted.")

        super().delete()

    def _reverse_moves(self, default_values_list=None, cancel=False):
        """Create reverse moves for this move"""
        if self.state != 'posted':
            raise ValidationError("Only posted moves can be reversed.")

        # Create reverse move
        reverse_vals = {
            'ref': f"Reversal of: {self.name}",
            'date': timezone.now().date(),
            'journal': self.journal,
            'move_type': 'entry',
            'company': self.company,
            'currency': self.currency,
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        if default_values_list:
            reverse_vals.update(default_values_list[0] if default_values_list else {})

        reverse_move = AccountMove.objects.create(**reverse_vals)

        # Create reverse lines
        for line in self.line_ids.all():
            if not line.display_type:  # Skip section/note lines
                AccountMoveLine.objects.create(
                    move=reverse_move,
                    account=line.account,
                    name=f"Reversal: {line.name}",
                    debit=line.credit,  # Swap debit/credit
                    credit=line.debit,
                    amount_currency=-line.amount_currency if line.amount_currency else 0,
                    currency=line.currency,
                    partner=line.partner,
                    date=line.date,  # Add missing date field
                    create_uid=self.create_uid,
                    write_uid=self.write_uid,
                )

        # Post the reverse move
        reverse_move.action_post()

        # Reconcile original and reverse moves if cancel=True
        if cancel:
            self._reconcile_with_reverse(reverse_move)

        return reverse_move

    def _reconcile_with_reverse(self, reverse_move):
        """Reconcile original move with its reverse"""
        # Group lines by account for reconciliation
        accounts = set()
        for line in self.line_ids.all():
            if line.account.reconcile and not line.display_type:
                accounts.add(line.account)

        for account in accounts:
            original_lines = self.line_ids.filter(account=account, display_type__isnull=True)
            reverse_lines = reverse_move.line_ids.filter(account=account, display_type__isnull=True)

            # Simple reconciliation - mark as reconciled
            for line in original_lines:
                line.reconciled = True
                line.save()
            for line in reverse_lines:
                line.reconciled = True
                line.save()

    def _get_integrity_hash_fields(self):
        """Get fields that affect integrity hash"""
        return ['name', 'date', 'journal', 'company']

    def _check_hash_integrity(self):
        """Check hash integrity for posted moves"""
        # Simplified implementation
        return True



    def _validate_balanced_for_posting(self):
        """Validate if the move is balanced for posting (raises ValidationError)"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())

        # Allow small rounding differences
        tolerance = 0.01
        difference = abs(total_debit - total_credit)

        if difference > tolerance:
            raise ValidationError(
                f"Move is not balanced. Difference: {difference:.2f} "
                f"(Debits: {total_debit:.2f}, Credits: {total_credit:.2f})"
            )

        return True

    # ===== ADVANCED INVOICING & PAYMENT METHODS =====

    def action_invoice_sent(self):
        """Mark invoice as sent and update tracking"""
        if not self.is_invoice():
            raise ValidationError("Only invoices can be marked as sent.")

        self.invoice_sent = True
        self.invoice_sent_date = timezone.now()
        self.save()

        # TODO: Send email notification
        # self._send_invoice_email()

    def action_register_payment(self, payment_vals):
        """Register payment against invoice"""
        if not self.is_invoice():
            raise ValidationError("Only invoices can receive payments.")

        if self.payment_state == 'paid':
            raise ValidationError("Invoice is already fully paid.")

        # Create payment record
        payment = AccountPayment.objects.create(**payment_vals)

        # Reconcile payment with invoice
        self._reconcile_payment(payment)

        return payment

    def _reconcile_payment(self, payment):
        """Reconcile payment with invoice"""
        # Get receivable/payable lines
        invoice_lines = self.line_ids.filter(
            account_id__account_type__in=['asset_receivable', 'liability_payable']
        )

        payment_lines = payment.move.line_ids.filter(
            account_id__account_type__in=['asset_receivable', 'liability_payable']
        )

        # Simple reconciliation
        for inv_line in invoice_lines:
            for pay_line in payment_lines:
                if inv_line.account == pay_line.account:
                    inv_line.reconciled = True
                    pay_line.reconciled = True
                    inv_line.save()
                    pay_line.save()

        # Update payment state
        self._compute_payment_state()

    def _compute_payment_state(self):
        """Compute payment state based on reconciled amounts"""
        if not self.is_invoice():
            return

        # Simplified computation
        if self.amount_residual <= 0:
            self.payment_state = 'paid'
        elif self.amount_residual < self.amount_total:
            self.payment_state = 'partial'
        else:
            self.payment_state = 'not_paid'

        self.save()

    def action_request_approval(self):
        """Request approval for the invoice"""
        if self.approval_status != 'draft':
            raise ValidationError("Only draft invoices can request approval.")

        self.approval_status = 'pending'
        self.save()

        # TODO: Send notification to approvers
        # self._send_approval_notification()

    def action_approve(self, user):
        """Approve the invoice"""
        if self.approval_status != 'pending':
            raise ValidationError("Only pending invoices can be approved.")

        self.approval_status = 'approved'
        self.approved_by = user
        self.approved_date = timezone.now()
        self.save()

    def action_reject(self, user, reason=None):
        """Reject the invoice"""
        if self.approval_status != 'pending':
            raise ValidationError("Only pending invoices can be rejected.")

        self.approval_status = 'rejected'
        self.approved_by = user
        self.approved_date = timezone.now()
        self.save()

        # TODO: Add rejection reason field and save it

    def create_recurring_invoice(self):
        """Create next recurring invoice"""
        if not self.is_recurring:
            raise ValidationError("Only recurring invoices can create recurring copies.")

        # Create copy with updated dates
        new_vals = {
            'name': 'New',  # Will be auto-generated
            'date': timezone.now().date(),
            'invoice_date': timezone.now().date(),
            'state': 'draft',
            'recurring_source': self,  # Pass the instance, not the ID
            'move_type': self.move_type,
            'partner': self.partner,
            'journal': self.journal,
            'company': self.company,
            'currency': self.currency,
            'is_recurring': self.is_recurring,
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        new_invoice = AccountMove.objects.create(**new_vals)

        # Copy invoice lines
        for line in self.line_ids.all():
            line_vals = {}
            for field in line._meta.fields:
                if field.name not in ['id', 'move', 'create_date', 'write_date']:
                    line_vals[field.name] = getattr(line, field.name)

            line_vals['move'] = new_invoice
            AccountMoveLine.objects.create(**line_vals)

        return new_invoice

    def _validate_invoice(self):
        """Validate and post invoice (for automated workflows)"""
        if self.state != 'draft':
            raise ValidationError("Only draft invoices can be validated.")

        # Perform validation checks
        self._check_balanced()

        # Post the invoice
        self.action_post()

        return True


class AccountMoveLineQuerySet(models.QuerySet):
    """Custom QuerySet for AccountMoveLine with reconciliation methods"""

    def reconcile(self, writeoff_acc=None, writeoff_journal=None):
        """Reconcile journal items"""
        if not self:
            return

        # Check that all lines are from reconcilable accounts
        non_reconcilable = [line for line in self if not line.account.reconcile]
        if non_reconcilable:
            raise ValidationError(f"Account {non_reconcilable[0].account_id.code} is not reconcilable.")

        # Check that all lines are from the same account
        accounts = set(line.account for line in self)
        if len(accounts) > 1:
            raise ValidationError("Cannot reconcile lines from different accounts.")

        # Check that all lines are posted
        unposted = [line for line in self if line.move.state != 'posted']
        if unposted:
            raise ValidationError("Cannot reconcile unposted journal items.")

        # Check balance
        total_balance = sum(line.balance for line in self)
        if abs(total_balance) > 0.01:  # Allow small rounding differences
            if not writeoff_acc_id:
                raise ValidationError(f"Cannot reconcile unbalanced items. Difference: {total_balance}")
            else:
                # Create writeoff entry
                self._create_writeoff_entry(writeoff_acc_id, writeoff_journal_id, total_balance)

        # Create full reconcile record
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admin_user = User.objects.first()  # Simplified for testing

        reconcile_name = self._generate_reconcile_ref()
        full_reconcile = AccountFullReconcile.objects.create(
            name=reconcile_name,
            create_uid=admin_user,
            write_uid=admin_user
        )

        # Mark lines as reconciled
        for line in self:
            line.reconciled = True
            line.full_reconcile = full_reconcile
            line.save()

        # Add lines to the reconcile record
        full_reconcile.reconciled_line_ids.set(self)

    def _generate_reconcile_ref(self):
        """Generate a unique reconciliation reference"""
        import uuid
        return f"REC{uuid.uuid4().hex[:8].upper()}"

    def remove_reconciliation(self):
        """Remove reconciliation from lines"""
        # Get full reconcile records to delete
        full_reconciles = set()
        for line in self:
            if line.full_reconcile:
                full_reconciles.add(line.full_reconcile)

        # Remove reconciliation from lines
        for line in self:
            line.reconciled = False
            line.full_reconcile = None
            line.save()

        # Delete full reconcile records
        for full_reconcile in full_reconciles:
            full_reconcile.delete()


class AccountMoveLineManager(models.Manager):
    """Custom manager for AccountMoveLine"""

    def get_queryset(self):
        return AccountMoveLineQuerySet(self.model, using=self._db)


class AccountMoveLine(BaseModel):
    """Journal Item model - equivalent to account.move.line in Odoo"""

    objects = AccountMoveLineManager()

    # Parent move
    move = models.ForeignKey(AccountMove, on_delete=models.CASCADE, related_name='line_ids')

    # Account and partner
    account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT)
    partner = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)

    # Amounts
    debit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    credit = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    balance = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Currency
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    amount_currency = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Description and reference
    name = models.CharField(max_length=255, help_text="Label")
    ref = models.CharField(max_length=255, blank=True, help_text="Reference")

    # Date (inherited from move but can be different for some entries)
    date = models.DateField()
    date_maturity = models.DateField(null=True, blank=True, help_text="Due date")

    # Tax information
    taxes = models.ManyToManyField(AccountTax, blank=True, related_name='move_line_taxs')
    tax_line = models.ForeignKey(AccountTax, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='tax_move_lines', help_text="Tax this line is part of")
    tax_base_amount = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)

    # Reconciliation
    reconciled = models.BooleanField(default=False)
    full_reconcile = models.ForeignKey('AccountFullReconcile', on_delete=models.SET_NULL,
                                        null=True, blank=True)

    # Matching and reconciliation
    matching_number = models.CharField(max_length=255, blank=True)

    # Product information (for invoice lines) - will be linked later when inventory app is created
    # product = models.ForeignKey('inventory.Product', on_delete=models.SET_NULL,
    #                              null=True, blank=True)
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=1.0)
    price_unit = models.DecimalField(max_digits=20, decimal_places=4, default=0.0)

    # Discount
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0.0)

    # Sequence for ordering
    sequence = models.IntegerField(default=10)

    # Display type for section and note lines
    DISPLAY_TYPE_CHOICES = [
        ('line_section', 'Section'),
        ('line_note', 'Note'),
    ]
    display_type = models.CharField(max_length=20, choices=DISPLAY_TYPE_CHOICES, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['move']),
            models.Index(fields=['account']),
            models.Index(fields=['partner']),
            models.Index(fields=['date']),
            models.Index(fields=['reconciled']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(debit__gte=0),
                name='check_debit_positive'
            ),
            models.CheckConstraint(
                check=models.Q(credit__gte=0),
                name='check_credit_positive'
            ),
        ]

    def __str__(self):
        return f"{self.move.name} - {self.account.code} - {self.name}"

    def save(self, *args, **kwargs):
        # Compute balance
        self.balance = Decimal(str(self.debit)) - Decimal(str(self.credit))

        # Set date from move if not provided
        if not self.date and self.move:
            self.date = self.move.date

        super().save(*args, **kwargs)

        # Update move totals
        if self.move:
            self.move._compute_amounts()

    def reconcile(self, writeoff_acc=None, writeoff_journal=None):
        """Reconcile this line with other lines"""
        # Check if account is reconcilable
        if not self.account.reconcile:
            raise ValidationError(f"Account {self.account.code} is not reconcilable.")

        # For single line reconciliation, we need to find matching lines
        # This is a simplified implementation - in real Odoo this is more complex
        if self.reconciled:
            raise ValidationError("Line is already reconciled.")

        # Mark as reconciled (simplified)
        self.reconciled = True
        self.save()

    def clean(self):
        super().clean()

        # Validate that debit and credit are not both set
        if self.debit > 0 and self.credit > 0:
            raise ValidationError("A journal item cannot have both debit and credit amounts.")

        # Validate account and journal consistency
        if (self.move and self.move.journal.account_control_ids.exists() and
            self.account not in self.move.journal.account_control_ids.all()):
            raise ValidationError(f"Account {self.account.code} is not allowed in journal {self.move.journal.code}")



class AccountFiscalPosition(BaseModel):
    """Fiscal Position model - equivalent to account.fiscal.position in Odoo"""

    name = models.CharField(max_length=255, help_text="Fiscal Position Name")
    active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.PROTECT)
    sequence = models.IntegerField(default=1)

    # Geographic criteria
    country = models.ForeignKey('core.Country', on_delete=models.PROTECT, null=True, blank=True)
    country_group = models.ForeignKey('core.CountryGroup', on_delete=models.PROTECT, null=True, blank=True)
    states = models.ManyToManyField('core.CountryState', blank=True)

    # VAT criteria
    vat_required = models.BooleanField(default=False, help_text="Apply only if partner has VAT")

    # Auto-detection
    auto_apply = models.BooleanField(default=False, help_text="Detect automatically")

    # Notes
    note = models.TextField(blank=True, help_text="Notes")

    class Meta:
        indexes = [
            models.Index(fields=['company']),
            models.Index(fields=['country']),
        ]

    def __str__(self):
        return self.name


class AccountPaymentTerm(BaseModel):
    """Payment Terms model - equivalent to account.payment.term in Odoo"""

    name = models.CharField(max_length=255, help_text="Payment Terms Name")
    active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.PROTECT, null=True, blank=True)
    sequence = models.IntegerField(default=10)

    # Configuration
    note = models.TextField(blank=True, help_text="Description on invoices")

    class Meta:
        indexes = [
            models.Index(fields=['company']),
        ]

    def __str__(self):
        return self.name


class AccountIncoterms(BaseModel):
    """Incoterms model - equivalent to account.incoterms in Odoo"""

    name = models.CharField(max_length=255, help_text="Incoterms Name")
    code = models.CharField(max_length=10, unique=True, help_text="Incoterms Code")
    active = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['code']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"


class AccountPayment(BaseModel):
    """Payment model - equivalent to account.payment in Odoo"""

    PAYMENT_TYPE_CHOICES = [
        ('outbound', 'Send Money'),
        ('inbound', 'Receive Money'),
        ('transfer', 'Internal Transfer'),
    ]

    PARTNER_TYPE_CHOICES = [
        ('customer', 'Customer'),
        ('supplier', 'Vendor'),
    ]

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('sent', 'Sent'),
        ('reconciled', 'Reconciled'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic fields
    name = models.CharField(max_length=255, default='/')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES)
    partner_type = models.CharField(max_length=20, choices=PARTNER_TYPE_CHOICES, null=True, blank=True)
    partner = models.ForeignKey(Partner, on_delete=models.PROTECT, null=True, blank=True)
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)
    date = models.DateField(default=timezone.now)
    ref = models.CharField(max_length=255, blank=True, help_text="Payment reference")

    # Journal and accounts
    journal = models.ForeignKey(AccountJournal, on_delete=models.PROTECT)
    destination_account = models.ForeignKey(AccountAccount, on_delete=models.PROTECT,
                                             related_name='payment_destination_account')

    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # State and reconciliation
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')
    move = models.ForeignKey(AccountMove, on_delete=models.PROTECT, null=True, blank=True,
                               help_text="Generated journal entry")
    reconciled_invoices = models.ManyToManyField(AccountMove, blank=True,
                                                   related_name='payment_reconciled_invoices',
                                                   help_text="Reconciled invoices")

    class Meta:
        verbose_name_plural = "Payments"

    def __str__(self):
        return f"{self.name} - {self.amount} {self.currency.name}"

    def action_post(self):
        """Post the payment and create journal entry"""
        if self.state != 'draft':
            raise ValidationError("Only draft payments can be posted.")

        # Create journal entry
        self._create_payment_entry()

        # Update state
        self.state = 'posted'
        self.save()

        # Auto-reconcile with invoices if possible
        self._auto_reconcile_invoices()

    def _create_payment_entry(self):
        """Create the journal entry for this payment"""
        # Prepare move values
        move_vals = {
            'journal': self.journal,
            'date': self.date,
            'ref': self.ref or f"Payment {self.name}",
            'company': self.company,
            'currency': self.currency,
            'move_type': 'entry',
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        # Create move
        move = AccountMove.objects.create(**move_vals)

        # Create payment lines
        self._create_payment_lines(move)

        # Post the move
        move.action_post()

        # Link to payment
        self.move = move
        self.save()

    def _auto_reconcile_invoices(self):
        """Auto-reconcile payment with related invoices"""
        # Simplified auto-reconciliation
        # In a full implementation, this would match payment lines with invoice lines
        pass

    def _create_payment_lines(self, move):
        """Create journal entry lines for the payment"""
        # Determine accounts based on payment type
        if self.payment_type == 'inbound':
            # Customer payment: Debit Bank, Credit Receivable
            debit_account = self.journal.default_account
            credit_account = self.destination_account
            debit_amount = self.amount
            credit_amount = self.amount
        elif self.payment_type == 'outbound':
            # Vendor payment: Debit Payable, Credit Bank
            debit_account = self.destination_account
            credit_account = self.journal.default_account
            debit_amount = self.amount
            credit_amount = self.amount
        else:
            # Transfer - more complex logic needed
            raise ValidationError("Transfer payments not yet implemented")

        # Create debit line
        AccountMoveLine.objects.create(
            move=move,
            account=debit_account,
            partner=self.partner,
            name=f"Payment {self.name}",
            debit=debit_amount,
            credit=0,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )

        # Create credit line
        AccountMoveLine.objects.create(
            move=move,
            account=credit_account,
            partner=self.partner,
            name=f"Payment {self.name}",
            debit=0,
            credit=credit_amount,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )


# ===== PAYMENT PROCESSING MODELS =====

class PaymentProvider(BaseModel):
    """Payment Provider model for online payments"""

    PROVIDER_CHOICES = [
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
        ('razorpay', 'Razorpay'),
        ('manual', 'Manual'),
    ]

    STATE_CHOICES = [
        ('disabled', 'Disabled'),
        ('enabled', 'Enabled'),
        ('test', 'Test Mode'),
    ]

    name = models.CharField(max_length=255, help_text="Provider Name")
    code = models.CharField(max_length=50, choices=PROVIDER_CHOICES, help_text="Provider Code")
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='disabled')

    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Configuration
    is_published = models.BooleanField(default=False, help_text="Published on Website")
    maximum_amount = models.DecimalField(max_digits=20, decimal_places=2, default=0.0,
                                       help_text="Maximum Payment Amount")

    # API Configuration (encrypted in production)
    api_key = models.CharField(max_length=255, blank=True, help_text="API Key")
    api_secret = models.CharField(max_length=255, blank=True, help_text="API Secret")
    webhook_secret = models.CharField(max_length=255, blank=True, help_text="Webhook Secret")

    def __str__(self):
        return self.name


class PaymentTransaction(BaseModel):
    """Payment Transaction model for online payments"""

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('authorized', 'Authorized'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
        ('error', 'Error'),
    ]

    OPERATION_CHOICES = [
        ('online_redirect', 'Online payment with redirection'),
        ('online_direct', 'Online direct payment'),
        ('online_token', 'Online payment by token'),
        ('validation', 'Validation of the payment method'),
        ('offline', 'Offline payment by token'),
        ('refund', 'Refund'),
    ]

    # Basic Information
    reference = models.CharField(max_length=255, unique=True, help_text="Transaction Reference")
    provider_reference = models.CharField(max_length=255, blank=True, help_text="Provider Reference")

    # Amount and Currency
    amount = models.DecimalField(max_digits=20, decimal_places=2, help_text="Transaction Amount")
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT)

    # Provider and State
    provider = models.ForeignKey(PaymentProvider, on_delete=models.PROTECT)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')
    operation = models.CharField(max_length=50, choices=OPERATION_CHOICES, blank=True)

    # Partner Information
    partner = models.ForeignKey(Partner, on_delete=models.PROTECT)
    partner_name = models.CharField(max_length=255, blank=True)
    partner_email = models.EmailField(blank=True)
    partner_phone = models.CharField(max_length=50, blank=True)

    # Company
    company = models.ForeignKey(Company, on_delete=models.PROTECT)

    # Related Documents
    invoices = models.ManyToManyField(AccountMove, blank=True, help_text="Related Invoices")
    payment = models.ForeignKey(AccountPayment, on_delete=models.SET_NULL, null=True, blank=True,
                                  help_text="Generated Payment")

    # Transaction Details
    provider_data = models.TextField(blank=True, help_text="Provider-specific data (JSON)")
    last_state_change = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.reference} - {self.amount} {self.currency.name}"

    def action_confirm(self):
        """Confirm the transaction"""
        if self.state != 'pending':
            raise ValidationError("Only pending transactions can be confirmed.")

        self.state = 'done'
        self.save()

        # Create payment record
        self._create_payment()

    def _create_payment(self):
        """Create payment record from transaction"""
        if self.payment:
            return self.payment

        # Get appropriate journal
        journal = AccountJournal.objects.filter(
            type='bank',
            company=self.company
        ).first()

        if not journal:
            raise ValidationError("No bank journal found for payment creation.")

        payment_vals = {
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner': self.partner,
            'amount': self.amount,
            'currency': self.currency,
            'journal': journal,
            'company': self.company,
            'ref': f"Online Payment: {self.reference}",
            'create_uid': self.create_uid,
            'write_uid': self.write_uid,
        }

        payment = AccountPayment.objects.create(**payment_vals)
        self.payment = payment
        self.save()

        return payment

        # Create credit line
        AccountMoveLine.objects.create(
            move=move,
            account=credit_account,
            partner=self.partner,
            name=f"Payment {self.name}",
            debit=0,
            credit=credit_amount,
            create_uid=self.create_uid,
            write_uid=self.write_uid,
        )

    def _auto_reconcile_invoices(self):
        """Automatically reconcile with outstanding invoices"""
        if not self.partner:
            return

        # Find outstanding invoices for this partner
        if self.payment_type == 'inbound':
            # Customer payment - find customer invoices
            outstanding_invoices = AccountMove.objects.filter(
                partner=self.partner,
                move_type='out_invoice',
                state='posted',
                payment_state__in=['not_paid', 'partial']
            )
        elif self.payment_type == 'outbound':
            # Vendor payment - find vendor bills
            outstanding_invoices = AccountMove.objects.filter(
                partner=self.partner,
                move_type='in_invoice',
                state='posted',
                payment_state__in=['not_paid', 'partial']
            )
        else:
            return

        # Simple reconciliation - match by amount
        remaining_amount = self.amount
        for invoice in outstanding_invoices:
            if remaining_amount <= 0:
                break

            invoice_amount = invoice.amount_residual
            if invoice_amount > 0:
                # Reconcile this invoice
                reconcile_amount = min(remaining_amount, invoice_amount)
                self._reconcile_with_invoice(invoice, reconcile_amount)
                remaining_amount -= reconcile_amount

    def _reconcile_with_invoice(self, invoice, amount):
        """Reconcile this payment with a specific invoice"""
        # Add to reconciled invoices
        self.reconciled_invoice_ids.add(invoice)

        # Update invoice payment state
        invoice._compute_payment_state()
        invoice.save()

        # Update payment state if fully reconciled
        if amount >= self.amount:
            self.state = 'reconciled'
            self.save()

    def _create_writeoff_entry(self, writeoff_acc_id, writeoff_journal_id, amount):
        """Create writeoff entry for reconciliation difference"""
        # This would create a journal entry for the writeoff amount
        # Simplified implementation
        pass

    def remove_move_reconcile(self):
        """Remove reconciliation from journal items"""
        for line in self:
            if line.reconciled:
                line.reconciled = False
                line.save()

    def _check_constrains_account_id_journal_id(self):
        """Check account and journal constraints"""
        for line in self:
            if line.display_type:
                continue

            account = line.account

            # Check deprecated accounts
            if account.deprecated:
                raise ValidationError(f"Account {account.code} ({account.name}) is deprecated.")

            # Check account type consistency with move type
            if line.move.is_sale_document():
                if account.account_type == 'liability_payable':
                    raise ValidationError(f"Account {account.code} is payable but used in sale operation.")
            elif line.move.is_purchase_document():
                if account.account_type == 'asset_receivable':
                    raise ValidationError(f"Account {account.code} is receivable but used in purchase operation.")

    def _check_reconcile_validity(self):
        """Check if lines can be reconciled"""
        # All lines must be from reconcilable accounts
        if not all(line.account.reconcile for line in self):
            raise ValidationError("All lines must be from reconcilable accounts.")

        # All lines must be posted
        if not all(line.move.state == 'posted' for line in self):
            raise ValidationError("All lines must be from posted moves.")

        # All lines must be from same account
        accounts = set(line.account for line in self)
        if len(accounts) > 1:
            raise ValidationError("All lines must be from the same account.")



    def delete(self):
        """Override delete to add business rules"""
        if self.move.state == 'posted':
            raise ValidationError("Cannot delete lines from posted moves.")

        if self.reconciled:
            raise ValidationError("Cannot delete reconciled lines.")

        super().delete()

class AccountFullReconcile(BaseModel):
    """Full Reconcile model - equivalent to account.full.reconcile in Odoo"""

    name = models.CharField(max_length=255)
    reconciled_lines = models.ManyToManyField(AccountMoveLine, related_name='full_reconciles')

    def __str__(self):
        return self.name

class AccountPartialReconcile(BaseModel):
    """Partial Reconcile model - equivalent to account.partial.reconcile in Odoo"""

    debit_move = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE,
                                    related_name='matched_debits')
    credit_move = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE,
                                     related_name='matched_credits')
    amount = models.DecimalField(max_digits=20, decimal_places=2)
    amount_currency = models.DecimalField(max_digits=20, decimal_places=2, default=0.0)
    currency = models.ForeignKey(Currency, on_delete=models.PROTECT, null=True, blank=True)
    full_reconcile = models.ForeignKey(AccountFullReconcile, on_delete=models.CASCADE,
                                        null=True, blank=True)

    def __str__(self):
        return f"Reconcile {self.amount} between {self.debit_move} and {self.credit_move}"
