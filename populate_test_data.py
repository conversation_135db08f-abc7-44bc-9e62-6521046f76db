#!/usr/bin/env python
"""
Simple script to populate test data via Django API
"""
import requests
import json

# API Configuration
BASE_URL = 'http://localhost:8000/api/v1'
headers = {'Content-Type': 'application/json'}

def create_test_data():
    print("🚀 Creating test data via API...")
    
    # First, let's try to get existing data
    try:
        # Check companies
        response = requests.get(f'{BASE_URL}/companies/', headers=headers)
        if response.status_code == 401:
            print("❌ Authentication required. Please login first.")
            return
        elif response.status_code == 200:
            companies = response.json()
            print(f"✅ Found {len(companies)} companies")
            
        # Check currencies
        response = requests.get(f'{BASE_URL}/currencies/', headers=headers)
        if response.status_code == 200:
            currencies = response.json()
            print(f"✅ Found {len(currencies)} currencies")
            
        # Check journals
        response = requests.get(f'{BASE_URL}/accounting/journals/', headers=headers)
        if response.status_code == 200:
            journals = response.json()
            print(f"✅ Found {len(journals)} journals")
            
        # Check accounts
        response = requests.get(f'{BASE_URL}/accounting/accounts/', headers=headers)
        if response.status_code == 200:
            accounts = response.json()
            print(f"✅ Found {len(accounts)} accounts")
            
        # Check partners
        response = requests.get(f'{BASE_URL}/partners/', headers=headers)
        if response.status_code == 200:
            partners = response.json()
            print(f"✅ Found {len(partners)} partners")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server. Please ensure it's running on port 8000.")
        return
    except Exception as e:
        print(f"❌ Error: {e}")
        return

if __name__ == "__main__":
    create_test_data()
