#!/usr/bin/env python
"""
Debug login process step by step
"""
import requests
import json

def debug_login():
    print("🔍 Debugging login process...")
    
    # Step 1: Test server connectivity
    print("\n1. Testing server connectivity...")
    try:
        response = requests.get('http://localhost:8000/api/v1/')
        print(f"   Django API: {response.status_code}")
    except Exception as e:
        print(f"   Django API: ERROR - {e}")
        return
    
    try:
        response = requests.get('http://localhost:3000/')
        print(f"   React App: {response.status_code}")
    except Exception as e:
        print(f"   React App: ERROR - {e}")
        return
    
    # Step 2: Test login endpoint
    print("\n2. Testing login endpoint...")
    login_url = 'http://localhost:8000/api/v1/auth/login/'
    
    # Test with correct credentials
    print("   Testing with admin/admin...")
    try:
        response = requests.post(login_url, json={
            'username': 'admin',
            'password': 'admin'
        })
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   User: {data.get('user', {}).get('username', 'N/A')}")
            print(f"   Token: {'✅ Present' if data.get('access') else '❌ Missing'}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Test with wrong credentials
    print("   Testing with wrong credentials...")
    try:
        response = requests.post(login_url, json={
            'username': 'admin',
            'password': 'wrong'
        })
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   ERROR: {e}")
    
    # Step 3: Test CORS
    print("\n3. Testing CORS headers...")
    try:
        response = requests.options(login_url, headers={
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        })
        print(f"   CORS preflight: {response.status_code}")
        cors_headers = {k: v for k, v in response.headers.items() if 'cors' in k.lower() or 'access-control' in k.lower()}
        for header, value in cors_headers.items():
            print(f"   {header}: {value}")
    except Exception as e:
        print(f"   CORS ERROR: {e}")
    
    # Step 4: Check user in database
    print("\n4. Checking user in database...")
    try:
        # Use Django shell to check user
        import os
        import sys
        import django
        
        # Setup Django
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'django_erp'))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_erp.settings')
        django.setup()
        
        from django.contrib.auth.models import User
        
        try:
            user = User.objects.get(username='admin')
            print(f"   User found: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Active: {user.is_active}")
            print(f"   Staff: {user.is_staff}")
            print(f"   Superuser: {user.is_superuser}")
            print(f"   Last login: {user.last_login}")
            
            # Test password
            from django.contrib.auth import authenticate
            auth_user = authenticate(username='admin', password='admin')
            print(f"   Password check: {'✅ Valid' if auth_user else '❌ Invalid'}")
            
        except User.DoesNotExist:
            print("   ❌ User not found!")
            
    except Exception as e:
        print(f"   Database check failed: {e}")
    
    print("\n✅ Debug complete!")

if __name__ == "__main__":
    debug_login()
