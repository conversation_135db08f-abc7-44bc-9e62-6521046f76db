import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { accountingAPI } from '../../services/api';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  PageHeader,
  PageTitle,
  Button,
  Table,
  LoadingSpinner,
  Section,
  Badge
} from '../../components/ui';
import { theme } from '../../components/ui/theme';

// Styled Components
const ActionButtons = styled.div`
  display: flex;
  gap: ${theme.spacing.sm};
`;

const StatusBadge = styled(Badge)`
  text-transform: capitalize;
`;

const AmountCell = styled.div`
  text-align: right;
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${props => props.amount > 0 ? theme.colors.success : theme.colors.textSecondary};
`;

const JournalEntryList = () => {
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchJournalEntries();
  }, []);

  const fetchJournalEntries = async () => {
    try {
      setLoading(true);
      const response = await accountingAPI.getJournalEntries();
      console.log('Journal entries API response:', response);
      console.log('Response type:', typeof response);
      console.log('Response.results:', response.results);
      console.log('Response keys:', Object.keys(response || {}));

      // Handle paginated response and filter out any null/undefined items
      const rawData = response.results || response || [];
      const filteredData = Array.isArray(rawData) ? rawData.filter(item => item != null) : [];
      console.log('Filtered data:', filteredData);
      setData(filteredData);
    } catch (error) {
      console.error('Error fetching journal entries:', error);
      setError('Failed to load journal entries');
      // Set empty array on error to prevent undefined data issues
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    navigate('/accounting/journal-entries/create');
  };

  const handleEdit = (id) => {
    navigate(`/accounting/journal-entries/edit/${id}`);
  };

  const handleView = (id) => {
    navigate(`/accounting/journal-entries/view/${id}`);
  };

  const handlePost = async (id) => {
    try {
      await accountingAPI.postJournalEntry(id);
      // Refresh the list
      fetchJournalEntries();
    } catch (error) {
      console.error('Error posting journal entry:', error);
      setError('Failed to post journal entry');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (state) => {
    switch (state) {
      case 'draft':
        return 'warning';
      case 'posted':
        return 'success';
      case 'cancel':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const columns = [
    {
      key: 'name',
      title: 'Entry Number',
      render: (item) => item?.name || 'Draft'
    },
    {
      key: 'date',
      title: 'Date',
      render: (item) => formatDate(item?.date)
    },
    {
      key: 'ref',
      title: 'Reference',
      render: (item) => item?.ref || '-'
    },
    {
      key: 'journal_name',
      title: 'Journal',
      render: (item) => item?.journal_name || '-'
    },
    {
      key: 'partner_name',
      title: 'Partner',
      render: (item) => item?.partner_name || '-'
    },
    {
      key: 'total_debit',
      title: 'Total Debit',
      render: (item) => (
        <AmountCell amount={item?.total_debit}>
          {formatCurrency(item?.total_debit)}
        </AmountCell>
      )
    },
    {
      key: 'total_credit',
      title: 'Total Credit',
      render: (item) => (
        <AmountCell amount={item?.total_credit}>
          {formatCurrency(item?.total_credit)}
        </AmountCell>
      )
    },
    {
      key: 'state',
      title: 'Status',
      render: (item) => (
        <StatusBadge color={getStatusColor(item?.state)}>
          {item?.state_display || item?.state || 'Unknown'}
        </StatusBadge>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (item) => {
        if (!item) return null;
        return (
          <ActionButtons>
            <Button
              size="sm"
              variant="outline"
              color="primary"
              onClick={() => handleView(item.id)}
            >
              View
            </Button>
            {item.state === 'draft' && (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  color="secondary"
                  onClick={() => handleEdit(item.id)}
                >
                  Edit
                </Button>
                <Button
                  size="sm"
                  color="success"
                  onClick={() => handlePost(item.id)}
                >
                  Post
                </Button>
              </>
            )}
          </ActionButtons>
        );
      }
    }
  ];

  if (loading) {
    return (
      <PageContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px' 
        }}>
          <LoadingSpinner size="lg" />
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Journal Entries</PageTitle>
        <Button color="primary" onClick={handleCreate}>
          Create Journal Entry
        </Button>
      </PageHeader>

      {error && (
        <Section>
          <div style={{ color: theme.colors.danger, textAlign: 'center' }}>
            {error}
          </div>
        </Section>
      )}

      <Section>
        <Table
          columns={columns}
          data={data}
          loading={loading}
          empty={
            <div style={{
              textAlign: 'center',
              padding: '40px 20px',
              color: '#6c757d'
            }}>
              <i className="fas fa-file-invoice" style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3>No journal entries found</h3>
              <p>Create your first journal entry to get started.</p>
            </div>
          }
        />
      </Section>
    </PageContainer>
  );
};

export default JournalEntryList;
