import React, { useState, useRef, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { theme } from '../theme';

const DataTableContainer = styled.div`
  width: 100%;
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.base};
  background: ${theme.colors.white};
  overflow: hidden;
`;

const TableWrapper = styled.div`
  overflow-x: auto;
  overflow-y: visible;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
`;

const TableHeader = styled.thead`
  background-color: ${theme.colors.bgSecondary};
  border-bottom: 2px solid ${theme.colors.border};
`;

const HeaderRow = styled.tr`
  height: 40px;
`;

const HeaderCell = styled.th.withConfig({
  shouldForwardProp: (prop) => !['minWidth', 'width', 'align'].includes(prop),
})`
  position: relative;
  padding: ${theme.spacing.sm} ${theme.spacing.xs};
  text-align: center;
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.textPrimary};
  border-right: 1px solid ${theme.colors.border};
  user-select: none;
  min-width: ${props => props.minWidth || '100px'};
  width: ${props => props.width || 'auto'};

  &:last-child {
    border-right: none;
  }
`;

const ResizeHandle = styled.div`
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 10;
  
  &:hover {
    background: ${theme.colors.primary};
  }
  
  &.resizing {
    background: ${theme.colors.primary};
  }
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  height: 40px;
  border-bottom: 1px solid ${theme.colors.border};
  
  &:hover {
    background-color: ${theme.colors.bgHover};
  }
  
  &.editing {
    background-color: ${theme.colors.bgSelected};
  }
`;

const TableCell = styled.td`
  padding: 0;
  border-right: 1px solid ${theme.colors.border};
  vertical-align: middle;
  position: relative;

  &:last-child {
    border-right: none;
  }
`;

const CellContent = styled.div`
  padding: ${theme.spacing.sm} ${theme.spacing.xs};
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: ${props => props.align === 'right' ? 'flex-end' : props.align === 'center' ? 'center' : 'flex-start'};
  cursor: ${props => props.editable ? 'pointer' : 'default'};
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.normal};

  &:hover {
    background-color: ${props => props.editable ? theme.colors.bgHover : 'transparent'};
  }
`;

const CellInput = styled.input`
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.normal};
  color: ${theme.colors.textPrimary};
  padding: ${theme.spacing.sm} ${theme.spacing.xs};
  
  &:focus {
    background-color: ${theme.colors.white};
    box-shadow: inset 0 0 0 2px ${theme.colors.primary};
  }
`;

const CellSelect = styled.select`
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
  font-weight: ${theme.typography.fontWeight.normal};
  color: ${theme.colors.textPrimary};
  padding: ${theme.spacing.sm} ${theme.spacing.xs};
  cursor: pointer;
  
  &:focus {
    background-color: ${theme.colors.white};
    box-shadow: inset 0 0 0 2px ${theme.colors.primary};
  }
  
  option {
    font-weight: 300;
  }
`;

const AddRowButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border: none;
  background: transparent;
  color: ${theme.colors.primary};
  font-family: ${theme.typography.fontFamily.odoo};
  font-size: ${theme.typography.fontSize.base};
  cursor: pointer;
  transition: all ${theme.transitions.fast};
  
  &:hover {
    background-color: ${theme.colors.bgHover};
    color: ${theme.colors.primaryDark};
  }
  
  i {
    font-size: ${theme.typography.fontSize.sm};
  }
`;

const DataTable = ({
  columns = [],
  data = [],
  onDataChange,
  onAddRow,
  onDeleteRow,
  editable = true,
  resizable = true,
  addRowText = "Add a line",
  className = '',
  ...props
}) => {
  const [columnWidths, setColumnWidths] = useState({});
  const [editingCell, setEditingCell] = useState(null);
  const [resizingColumn, setResizingColumn] = useState(null);
  const tableRef = useRef(null);
  const resizeStartX = useRef(0);
  const resizeStartWidth = useRef(0);

  // Initialize column widths
  useEffect(() => {
    const initialWidths = {};
    columns.forEach((col, index) => {
      if (col.width) {
        initialWidths[index] = col.width;
      }
    });
    setColumnWidths(initialWidths);
  }, [columns]);

  const handleCellClick = useCallback((rowIndex, colIndex) => {
    if (editable && columns[colIndex]?.editable !== false) {
      setEditingCell({ row: rowIndex, col: colIndex });
    }
  }, [editable, columns]);

  const handleCellChange = useCallback((rowIndex, colIndex, value) => {
    if (onDataChange) {
      const newData = [...data];
      const column = columns[colIndex];
      newData[rowIndex] = {
        ...newData[rowIndex],
        [column.key]: value
      };
      onDataChange(newData);
    }
  }, [data, columns, onDataChange]);

  const handleCellBlur = useCallback(() => {
    setEditingCell(null);
  }, []);

  const handleKeyDown = useCallback((e, rowIndex, colIndex) => {
    if (e.key === 'Enter' || e.key === 'Tab') {
      e.preventDefault();
      setEditingCell(null);
      
      // Move to next cell
      if (e.key === 'Tab') {
        const nextCol = colIndex + 1;
        const nextRow = nextCol >= columns.length ? rowIndex + 1 : rowIndex;
        const finalCol = nextCol >= columns.length ? 0 : nextCol;
        
        if (nextRow < data.length) {
          setTimeout(() => {
            setEditingCell({ row: nextRow, col: finalCol });
          }, 0);
        }
      }
    } else if (e.key === 'Escape') {
      setEditingCell(null);
    }
  }, [columns.length, data.length]);

  const startResize = useCallback((e, colIndex) => {
    e.preventDefault();
    setResizingColumn(colIndex);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[colIndex] || 150;
    
    const handleMouseMove = (e) => {
      const diff = e.clientX - resizeStartX.current;
      const newWidth = Math.max(50, resizeStartWidth.current + diff);
      setColumnWidths(prev => ({
        ...prev,
        [colIndex]: `${newWidth}px`
      }));
    };
    
    const handleMouseUp = () => {
      setResizingColumn(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [columnWidths]);

  const renderCellContent = (row, column, rowIndex, colIndex) => {
    const isEditing = editingCell?.row === rowIndex && editingCell?.col === colIndex;
    const value = row[column.key] || '';

    if (isEditing) {
      // For specialized input types, use the render function if available
      if (column.render && (column.type === 'account' || column.type === 'partner' || column.type === 'currency')) {
        return column.render(value, { ...row, editing: true }, rowIndex, {
          onChange: (e) => handleCellChange(rowIndex, colIndex, e.target.value),
          onBlur: handleCellBlur,
          onKeyDown: (e) => handleKeyDown(e, rowIndex, colIndex),
          autoFocus: true,
          name: column.key
        });
      } else if (column.type === 'select' && column.options) {
        return (
          <CellSelect
            value={value}
            onChange={(e) => handleCellChange(rowIndex, colIndex, e.target.value)}
            onBlur={handleCellBlur}
            onKeyDown={(e) => handleKeyDown(e, rowIndex, colIndex)}
            autoFocus
          >
            <option value="">Select...</option>
            {column.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </CellSelect>
        );
      } else {
        return (
          <CellInput
            type={column.type || 'text'}
            value={value}
            onChange={(e) => handleCellChange(rowIndex, colIndex, e.target.value)}
            onBlur={handleCellBlur}
            onKeyDown={(e) => handleKeyDown(e, rowIndex, colIndex)}
            autoFocus
          />
        );
      }
    }

    // Render display value
    if (column.render) {
      return column.render(value, row, rowIndex);
    }

    return value || '';
  };

  return (
    <DataTableContainer className={className} {...props}>
      <TableWrapper>
        <Table ref={tableRef}>
          <TableHeader>
            <HeaderRow>
              {columns.map((column, colIndex) => (
                <HeaderCell
                  key={column.key}
                  align={column.align}
                  width={columnWidths[colIndex] || column.width}
                  minWidth={column.minWidth}
                >
                  {column.title}
                  {resizable && (
                    <ResizeHandle
                      className={resizingColumn === colIndex ? 'resizing' : ''}
                      onMouseDown={(e) => startResize(e, colIndex)}
                    />
                  )}
                </HeaderCell>
              ))}
            </HeaderRow>
          </TableHeader>
          <TableBody>
            {data.map((row, rowIndex) => (
              <TableRow
                key={rowIndex}
                className={editingCell?.row === rowIndex ? 'editing' : ''}
              >
                {columns.map((column, colIndex) => (
                  <TableCell
                    key={column.key}
                    onClick={() => handleCellClick(rowIndex, colIndex)}
                  >
                    <CellContent
                      editable={editable && column.editable !== false}
                      align={column.align}
                    >
                      {renderCellContent(row, column, rowIndex, colIndex)}
                    </CellContent>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableWrapper>
      
      {editable && onAddRow && (
        <AddRowButton onClick={onAddRow}>
          <i className="fas fa-plus" />
          {addRowText}
        </AddRowButton>
      )}
    </DataTableContainer>
  );
};

DataTable.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    width: PropTypes.string,
    minWidth: PropTypes.string,
    align: PropTypes.oneOf(['left', 'center', 'right']),
    type: PropTypes.oneOf(['text', 'number', 'select', 'currency']),
    editable: PropTypes.bool,
    options: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })),
    render: PropTypes.func,
  })).isRequired,
  data: PropTypes.array.isRequired,
  onDataChange: PropTypes.func,
  onAddRow: PropTypes.func,
  onDeleteRow: PropTypes.func,
  editable: PropTypes.bool,
  resizable: PropTypes.bool,
  addRowText: PropTypes.string,
  className: PropTypes.string,
};

export default DataTable;
